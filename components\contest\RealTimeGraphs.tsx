'use client';

import { useState, useEffect, useRef } from 'react';

interface DataPoint {
  time: number;
  wpm: number;
  accuracy: number;
}

interface ParticipantData {
  id: string;
  name: string;
  data: DataPoint[];
  color: string;
}

interface RealTimeGraphsProps {
  participants: {
    id: string;
    name: string;
    wpm: number;
    accuracy: number;
  }[];
  isActive: boolean;
}

export default function RealTimeGraphs({ participants, isActive }: RealTimeGraphsProps) {
  const [participantData, setParticipantData] = useState<ParticipantData[]>([]);
  const [timeElapsed, setTimeElapsed] = useState(0);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();

  const colors = [
    '#f97316', // orange
    '#3b82f6', // blue
    '#10b981', // green
    '#f59e0b', // yellow
    '#ef4444', // red
    '#8b5cf6', // purple
    '#06b6d4', // cyan
    '#84cc16', // lime
  ];

  useEffect(() => {
    // Initialize participant data
    const initialData = participants.map((p, index) => ({
      id: p.id,
      name: p.name,
      data: [],
      color: colors[index % colors.length]
    }));
    setParticipantData(initialData);
  }, [participants]);

  useEffect(() => {
    if (isActive) {
      const interval = setInterval(() => {
        setTimeElapsed(prev => prev + 1);
        
        // Update participant data
        setParticipantData(prev => 
          prev.map(pd => {
            const participant = participants.find(p => p.id === pd.id);
            if (participant) {
              const newDataPoint: DataPoint = {
                time: timeElapsed,
                wpm: participant.wpm + Math.random() * 10 - 5, // Add some variation for demo
                accuracy: Math.max(85, participant.accuracy + Math.random() * 6 - 3)
              };
              return {
                ...pd,
                data: [...pd.data.slice(-29), newDataPoint] // Keep last 30 points
              };
            }
            return pd;
          })
        );
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [isActive, timeElapsed, participants]);

  useEffect(() => {
    drawGraphs();
  }, [participantData]);

  const drawGraphs = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width * window.devicePixelRatio;
    canvas.height = rect.height * window.devicePixelRatio;
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio);

    const width = rect.width;
    const height = rect.height;

    // Clear canvas
    ctx.fillStyle = '#1f2937';
    ctx.fillRect(0, 0, width, height);

    // Draw grid
    ctx.strokeStyle = '#374151';
    ctx.lineWidth = 1;
    
    // Vertical grid lines
    for (let i = 0; i <= 10; i++) {
      const x = (width / 10) * i;
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, height);
      ctx.stroke();
    }
    
    // Horizontal grid lines
    for (let i = 0; i <= 5; i++) {
      const y = (height / 5) * i;
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(width, y);
      ctx.stroke();
    }

    // Draw WPM lines
    participantData.forEach(pd => {
      if (pd.data.length < 2) return;

      ctx.strokeStyle = pd.color;
      ctx.lineWidth = 3;
      ctx.beginPath();

      pd.data.forEach((point, index) => {
        const x = (index / Math.max(pd.data.length - 1, 1)) * width;
        const y = height - (point.wpm / 100) * height; // Assuming max 100 WPM for scale

        if (index === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      });

      ctx.stroke();

      // Draw points
      ctx.fillStyle = pd.color;
      pd.data.forEach((point, index) => {
        const x = (index / Math.max(pd.data.length - 1, 1)) * width;
        const y = height - (point.wpm / 100) * height;
        
        ctx.beginPath();
        ctx.arc(x, y, 4, 0, 2 * Math.PI);
        ctx.fill();
      });
    });

    // Draw labels
    ctx.fillStyle = '#9ca3af';
    ctx.font = '12px sans-serif';
    ctx.textAlign = 'left';
    ctx.fillText('0 WPM', 5, height - 5);
    ctx.fillText('100 WPM', 5, 15);
  };

  return (
    <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
      <h3 className="text-lg font-semibold text-white mb-4">Real-time Performance</h3>
      
      {/* Legend */}
      <div className="flex flex-wrap gap-4 mb-4">
        {participantData.map(pd => (
          <div key={pd.id} className="flex items-center gap-2">
            <div 
              className="w-4 h-4 rounded-full"
              style={{ backgroundColor: pd.color }}
            ></div>
            <span className="text-white text-sm font-medium">
              {pd.name}
              {pd.id === '1' && <span className="text-blue-400 ml-1">(You)</span>}
            </span>
          </div>
        ))}
      </div>

      {/* Graph Canvas */}
      <div className="relative bg-gray-900/50 rounded-lg p-4 mb-4" style={{ height: '300px' }}>
        <canvas
          ref={canvasRef}
          className="w-full h-full"
          style={{ width: '100%', height: '100%' }}
        />
        
        {!isActive && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-800/50 rounded-lg">
            <span className="text-gray-400 font-medium">Graph will update during contest</span>
          </div>
        )}
      </div>

      {/* Current Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {participantData.map(pd => {
          const latestData = pd.data[pd.data.length - 1];
          return (
            <div key={pd.id} className="bg-gray-700/50 rounded-lg p-3 text-center">
              <div className="flex items-center justify-center gap-2 mb-2">
                <div 
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: pd.color }}
                ></div>
                <span className="text-white text-sm font-medium truncate">
                  {pd.name}
                </span>
              </div>
              {latestData ? (
                <>
                  <div className="text-lg font-bold text-white">{Math.round(latestData.wpm)}</div>
                  <div className="text-xs text-gray-400">WPM</div>
                  <div className="text-sm text-gray-300 mt-1">{Math.round(latestData.accuracy)}% acc</div>
                </>
              ) : (
                <div className="text-gray-400 text-sm">No data</div>
              )}
            </div>
          );
        })}
      </div>

      {/* Time Elapsed */}
      {isActive && (
        <div className="text-center mt-4">
          <span className="text-gray-400 text-sm">Time elapsed: </span>
          <span className="text-white font-bold">{Math.floor(timeElapsed / 60)}:{(timeElapsed % 60).toString().padStart(2, '0')}</span>
        </div>
      )}
    </div>
  );
}
