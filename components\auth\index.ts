// Authentication Components
export { default as AuthButton } from './AuthButton';
export { default as AuthGuard } from './AuthGuard';
export { default as ProtectedRoute } from './ProtectedRoute';
export { default as SocialAuthButtons } from './SocialAuthButtons';
export { default as UserAvatar } from './UserAvatar';

// Re-export the existing AuthProvider
export { default as AuthProvider } from '../providers/AuthProvider';

// Types
export type { AuthUser, UseAuthReturn } from '../../hooks/useAuth';
