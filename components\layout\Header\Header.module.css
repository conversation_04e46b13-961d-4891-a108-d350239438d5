/* Mobile Menu Styles */
.mobileMenu {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 50;
  transition: opacity 0.3s ease-in-out;
  opacity: 0;
  pointer-events: none;
}

.mobileMenu.open {
  opacity: 1;
  pointer-events: auto;
}

.mobileMenuContent {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: slideInFromRight 0.3s ease-out;
}

/* Professional Dropdown Animations */
.userDropdown {
  animation: dropdownSlideIn 0.2s ease-out;
  transform-origin: top right;
}

.userDropdown::before {
  content: '';
  position: absolute;
  top: -8px;
  right: 20px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid white;
  filter: drop-shadow(0 -2px 4px rgba(0, 0, 0, 0.1));
}

.menuItem {
  transition: all 0.2s ease-in-out;
}

.menuItem:hover {
  transform: translateX(4px);
}

/* Keyframe Animations */
@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes dropdownSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Button Hover Effects */
.authButton {
  position: relative;
  overflow: hidden;
}

.authButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.authButton:hover::before {
  left: 100%;
}

/* Professional styling for user avatar */
.userAvatar {
  transition: all 0.3s ease;
}

.userAvatar:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(255, 66, 7, 0.3);
}

/* Loading spinner */
.loadingSpinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #ff4207;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
