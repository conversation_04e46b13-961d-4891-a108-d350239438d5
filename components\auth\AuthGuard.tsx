'use client';

import { useSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, ReactNode } from 'react';
import LoadingSpinner from '@/components/common/LoadingSpinner';

interface AuthGuardProps {
  children: ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
  fallback?: ReactNode;
}

/**
 * AuthGuard component that protects routes and handles authentication redirects
 */
export default function AuthGuard({ 
  children, 
  requireAuth = true,
  redirectTo = '/sign-in',
  fallback 
}: AuthGuardProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    if (status === 'loading') return; // Still loading

    if (requireAuth && status === 'unauthenticated') {
      // Get current path for redirect after login
      const currentPath = window.location.pathname + window.location.search;
      const redirectUrl = `${redirectTo}?redirect=${encodeURIComponent(currentPath)}`;
      router.push(redirectUrl);
    }

    // Handle redirect after successful authentication
    if (status === 'authenticated' && !requireAuth) {
      const redirectParam = searchParams.get('redirect');
      if (redirectParam) {
        router.push(redirectParam);
      }
    }
  }, [status, requireAuth, redirectTo, router, searchParams]);

  // Show loading state
  if (status === 'loading') {
    return fallback || (
      <div className="min-h-screen flex items-center justify-center bg-gray-950">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="text-gray-400 mt-4">Authenticating...</p>
        </div>
      </div>
    );
  }

  // If auth is required but user is not authenticated, don't render children
  if (requireAuth && status === 'unauthenticated') {
    return null;
  }

  // Render children if auth requirements are met
  return <>{children}</>;
}
