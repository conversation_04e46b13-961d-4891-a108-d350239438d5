# Next-Auth Configuration
NEXTAUTH_URL=http://localhost:3000
# For production
# NEXTAUTH_URL=https://typingcomco.vercel.app

NEXTAUTH_SECRET=d1c83d0dabb33575b77ae2b1662f0bc4

# GitHub OAuth (for Next-Auth)
GITHUB_ID=********************
GITHUB_SECRET=****************************************

# Google OAuth (for Next-Auth)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Email Configuration (for password reset)
EMAIL_SERVER_HOST=smtp.gmail.com
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=<EMAIL>
EMAIL_SERVER_PASSWORD=12345
EMAIL_FROM=<EMAIL>

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000

# MongoDB Atlas
MONGODB_URI=mongodb+srv://jasgigli:<EMAIL>/?retryWrites=true&w=majority&appName=jasgigli

# API URLs
NEXT_PUBLIC_API_URL=http://localhost:3000/api
# For production
# NEXT_PUBLIC_API_URL=https://typingcomco.vercel.app/api

# Socket.io
NEXT_PUBLIC_SOCKET_URL=http://localhost:3000
# For production
# NEXT_PUBLIC_SOCKET_URL=https://typingcomco.vercel.app

# Webhook Secret (for external services)
WEBHOOK_SECRET=your_webhook_secret

# Cron Secret
CRON_SECRET=your_cron_secret

# Email Configuration (for password reset, etc.)
EMAIL_SERVER_HOST=smtp.gmail.com
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=<EMAIL>
EMAIL_SERVER_PASSWORD=12345
EMAIL_FROM=<EMAIL>

# Gemini API Key
NEXT_PUBLIC_GEMINI_API_KEY="AIzaSyAoHAatVLl2xQVeNx2SVIIHg3V02Q2qoV8"








