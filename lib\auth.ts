import connectDB from '@/lib/db';
import User from '@/lib/models/User';
import bcrypt from 'bcryptjs';
import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import GithubProvider from 'next-auth/providers/github';
import GoogleProvider from 'next-auth/providers/google';

export const authOptions: NextAuthOptions = {
  // Configure authentication providers
  providers: [
    GithubProvider({
      clientId: process.env.GITHUB_ID || '',
      clientSecret: process.env.GITHUB_SECRET || '',
    }),
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID || '',
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
    }),
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error('Email and password are required');
        }

        try {
          await connectDB();
          const user = await User.findOne({ email: credentials.email });

          if (!user) {
            throw new Error('No user found with this email');
          }

          // Check if user has a password (not a social login user)
          if (!user.password || user.password === 'clerk-managed') {
            throw new Error('Please sign in with your social account');
          }

          const isPasswordValid = await bcrypt.compare(credentials.password, user.password);

          if (!isPasswordValid) {
            throw new Error('Invalid password');
          }

          return {
            id: user._id.toString(),
            email: user.email,
            name: user.name,
            image: user.avatar || null,
          };
        } catch (error) {
          console.error('Auth error:', error);
          throw error;
        }
      },
    }),
  ],
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  pages: {
    signIn: '/sign-in',
    error: '/sign-in', // Redirect to sign-in page on error
  },
  callbacks: {
    async jwt({ token, user, account }) {
      if (user) {
        token.id = user.id;
        token.email = user.email;
        token.name = user.name;
        token.image = user.image;
      }

      // Handle OAuth providers (GitHub and Google)
      if (account?.provider === 'github' || account?.provider === 'google') {
        try {
          await connectDB();
          let dbUser = await User.findOne({ email: user?.email });

          if (!dbUser) {
            // Create new user for OAuth
            dbUser = await User.create({
              email: user?.email,
              name: user?.name,
              avatar: user?.image,
              provider: account.provider,
              providerId: account.providerAccountId,
              password: `${account.provider}-oauth`, // Mark as OAuth user
              isEmailVerified: true, // OAuth emails are pre-verified
            });
          } else {
            // Update existing user with OAuth info if not already set
            if (!dbUser.provider || dbUser.provider === 'credentials') {
              dbUser.provider = account.provider;
              dbUser.providerId = account.providerAccountId;
              dbUser.avatar = user?.image || dbUser.avatar;
              dbUser.isEmailVerified = true;
              await dbUser.save();
            }
          }

          token.id = dbUser._id.toString();
        } catch (error) {
          console.error(`${account.provider} OAuth error:`, error);
        }
      }

      return token;
    },
    async session({ session, token }) {
      if (token && session.user) {
        session.user.id = token.id as string;
        session.user.email = token.email as string;
        session.user.name = token.name as string;
        session.user.image = token.image as string;
      }
      return session;
    },
    async signIn({ account }) {
      // For OAuth providers, always allow sign in
      if (account?.provider === 'github' || account?.provider === 'google') {
        return true;
      }

      // For credentials provider, validation is handled in the authorize function
      return true;
    },
  },
  secret: process.env.NEXTAUTH_SECRET,
  debug: process.env.NODE_ENV === 'development',
};
