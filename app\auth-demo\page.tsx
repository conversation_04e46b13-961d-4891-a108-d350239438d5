'use client';

import { 
  AuthButton, 
  AuthGuard, 
  SocialAuthButtons, 
  UserAvatar 
} from '@/components/auth';
import { useAuth } from '@/hooks';

export default function AuthDemoPage() {
  const { user, isAuthenticated, isLoading } = useAuth();

  return (
    <div className="min-h-screen bg-gray-950 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8 text-center">
          Authentication Components Demo
        </h1>

        {/* Auth Status */}
        <div className="bg-gray-900 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-white mb-4">Authentication Status</h2>
          <div className="space-y-2 text-gray-300">
            <p>Loading: {isLoading ? 'Yes' : 'No'}</p>
            <p>Authenticated: {isAuthenticated ? 'Yes' : 'No'}</p>
            <p>User: {user ? user.name : 'None'}</p>
          </div>
        </div>

        {/* Auth Buttons */}
        <div className="bg-gray-900 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-white mb-4">Auth Buttons</h2>
          <div className="space-y-4">
            <div>
              <h3 className="text-lg text-white mb-2">Primary Button</h3>
              <AuthButton variant="primary" />
            </div>
            <div>
              <h3 className="text-lg text-white mb-2">Secondary Button</h3>
              <AuthButton variant="secondary" />
            </div>
            <div>
              <h3 className="text-lg text-white mb-2">Outline Button</h3>
              <AuthButton variant="outline" />
            </div>
          </div>
        </div>

        {/* User Avatar */}
        <div className="bg-gray-900 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-white mb-4">User Avatar</h2>
          <div className="space-y-4">
            <div>
              <h3 className="text-lg text-white mb-2">Small Avatar</h3>
              <UserAvatar size={32} />
            </div>
            <div>
              <h3 className="text-lg text-white mb-2">Medium Avatar with Name</h3>
              <UserAvatar size={48} showName />
            </div>
            <div>
              <h3 className="text-lg text-white mb-2">Large Avatar with Name and Email</h3>
              <UserAvatar size={64} showName showEmail />
            </div>
          </div>
        </div>

        {/* Social Auth Buttons */}
        <div className="bg-gray-900 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-white mb-4">Social Authentication</h2>
          <div className="max-w-md">
            <SocialAuthButtons buttonText="signin" />
          </div>
        </div>

        {/* Protected Content */}
        <div className="bg-gray-900 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-white mb-4">Protected Content</h2>
          <AuthGuard requireAuth={true}>
            <div className="bg-green-900 border border-green-700 rounded-lg p-4">
              <p className="text-green-100">
                🎉 This content is only visible to authenticated users!
              </p>
              <p className="text-green-200 text-sm mt-2">
                Welcome, {user?.name}! Your email is {user?.email}
              </p>
            </div>
          </AuthGuard>
          
          <AuthGuard requireAuth={false}>
            <div className="bg-blue-900 border border-blue-700 rounded-lg p-4 mt-4">
              <p className="text-blue-100">
                ℹ️ This content is visible to everyone (public content)
              </p>
            </div>
          </AuthGuard>
        </div>
      </div>
    </div>
  );
}
