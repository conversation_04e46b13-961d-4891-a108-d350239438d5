'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import ContestLobby from '@/components/contest/ContestLobby';
import CreateRoom from '@/components/contest/CreateRoom';
import JoinRoom from '@/components/contest/JoinRoom';
import ContestRoom from '@/components/contest/ContestRoom';

type ContestView = 'lobby' | 'create' | 'join' | 'room';

interface RoomData {
  id: string;
  name: string;
  createdBy: string;
  participants: string[];
  settings: {
    duration: number;
    difficulty: string;
    maxParticipants: number;
  };
  status: 'waiting' | 'active' | 'finished';
}

export default function ContestPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [currentView, setCurrentView] = useState<ContestView>('lobby');
  const [currentRoom, setCurrentRoom] = useState<RoomData | null>(null);

  // Redirect to sign-in if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/sign-in?callbackUrl=/contest');
    }
  }, [status, router]);

  // Show loading while checking authentication
  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white text-lg">Loading Contest...</p>
        </div>
      </div>
    );
  }

  // Don't render anything if not authenticated (will redirect)
  if (status === 'unauthenticated') {
    return null;
  }

  const handleCreateRoom = (roomData: Omit<RoomData, 'id' | 'participants' | 'status'>) => {
    // TODO: Implement room creation API call
    const newRoom: RoomData = {
      ...roomData,
      id: Math.random().toString(36).substr(2, 9),
      participants: [session?.user?.email || ''],
      status: 'waiting'
    };
    setCurrentRoom(newRoom);
    setCurrentView('room');
  };

  const handleJoinRoom = (roomId: string) => {
    // TODO: Implement room joining API call
    console.log('Joining room:', roomId);
    setCurrentView('room');
  };

  const handleLeaveRoom = () => {
    setCurrentRoom(null);
    setCurrentView('lobby');
  };

  const renderCurrentView = () => {
    switch (currentView) {
      case 'create':
        return (
          <CreateRoom
            onCreateRoom={handleCreateRoom}
            onBack={() => setCurrentView('lobby')}
          />
        );
      case 'join':
        return (
          <JoinRoom
            onJoinRoom={handleJoinRoom}
            onBack={() => setCurrentView('lobby')}
          />
        );
      case 'room':
        return (
          <ContestRoom
            room={currentRoom}
            onLeaveRoom={handleLeaveRoom}
          />
        );
      default:
        return (
          <ContestLobby
            onCreateRoom={() => setCurrentView('create')}
            onJoinRoom={() => setCurrentView('join')}
          />
        );
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
              Typing <span className="text-orange-500">Contest</span>
            </h1>
            <p className="text-gray-300 text-lg max-w-2xl mx-auto">
              Challenge your friends in real-time typing competitions. Create rooms, share links, 
              and see who types the fastest with live progress tracking!
            </p>
          </div>

          {/* Main Content */}
          {renderCurrentView()}
        </div>
      </div>
    </div>
  );
}
