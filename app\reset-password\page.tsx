'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { FaEye, FaEyeSlash } from 'react-icons/fa';
import { FaLinkedinIn, FaXTwitter } from 'react-icons/fa6';
import { toast } from 'react-toastify';
import { z } from 'zod';

const resetPasswordSchema = z
  .object({
    password: z.string().min(6, 'Password must be at least 6 characters'),
    confirmPassword: z.string().min(6, 'Confirm password must be at least 6 characters'),
  })
  .refine(data => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;

export default function ResetPasswordPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [token, setToken] = useState<string | null>(null);

  const router = useRouter();
  const searchParams = useSearchParams();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ResetPasswordFormData>({
    resolver: zodResolver(resetPasswordSchema),
  });

  useEffect(() => {
    const tokenParam = searchParams.get('token');
    if (!tokenParam) {
      toast.error('Invalid reset link. Please request a new password reset.');
      router.push('/forgot-password');
    } else {
      setToken(tokenParam);
    }
  }, [searchParams, router]);

  const onSubmit: SubmitHandler<ResetPasswordFormData> = async data => {
    if (!token) {
      toast.error('Invalid reset token');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token,
          password: data.password,
          confirmPassword: data.confirmPassword,
        }),
      });

      if (response.ok) {
        setIsSuccess(true);
        toast.success('Password reset successfully!');
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || 'Failed to reset password. Please try again.');
      }
    } catch (error) {
      console.error('Reset password error:', error);
      toast.error('An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (!token) {
    return (
      <div className="min-h-screen bg-[#171717] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500 mx-auto"></div>
          <p className="text-gray-400 mt-4">Validating reset link...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#171717] flex font-sans">
      {/* Left Branding Panel */}
      <div className="hidden lg:flex flex-col w-1/2 bg-dot-pattern items-center justify-center p-12 relative">
        {/* Logo */}
        <div className="flex items-center">
          <span className="text-7xl font-bold text-white tracking-tight">typing</span>
          <div className="ml-1 -mt-1 flex items-center justify-center w-10 h-10 bg-red-500 rounded-full">
            <span className="text-white text-sm font-semibold">.co</span>
          </div>
        </div>

        {/* Social Icons at the bottom */}
        <div className="absolute bottom-10 left-10 flex space-x-4">
          <Link href="#" aria-label="X (Twitter)" className="text-gray-400 hover:text-white p-1">
            <FaXTwitter size={20} />
          </Link>
          <span className="text-gray-500 text-xl leading-none select-none" aria-hidden="true">
            •
          </span>
          <Link href="#" aria-label="LinkedIn" className="text-gray-400 hover:text-white p-1">
            <FaLinkedinIn size={20} />
          </Link>
        </div>
      </div>

      {/* Right Form Panel */}
      <div className="w-full lg:w-1/2 bg-[#212121] flex flex-col items-center justify-center p-6 sm:p-10 relative">
        <div className="w-full max-w-sm">
          {!isSuccess ? (
            <>
              {/* Header */}
              <div className="text-center mb-8">
                <h1 className="text-3xl font-bold text-white mb-2">Reset Password</h1>
                <p className="text-gray-400">Enter your new password</p>
              </div>

              {/* Form */}
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-5">
                <div className="relative">
                  <input
                    {...register('password')}
                    type={showPassword ? 'text' : 'password'}
                    className="w-full px-4 py-3 pr-12 bg-[#2C2C2C] border border-transparent text-gray-300 placeholder-gray-500 focus:ring-2 focus:ring-orange-500 focus:border-transparent rounded-xl outline-none transition-all"
                    placeholder="New password"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
                  >
                    {showPassword ? <FaEyeSlash size={18} /> : <FaEye size={18} />}
                  </button>
                  {errors.password && (
                    <p className="mt-1.5 text-xs text-red-400">{errors.password.message}</p>
                  )}
                </div>

                <div className="relative">
                  <input
                    {...register('confirmPassword')}
                    type={showConfirmPassword ? 'text' : 'password'}
                    className="w-full px-4 py-3 pr-12 bg-[#2C2C2C] border border-transparent text-gray-300 placeholder-gray-500 focus:ring-2 focus:ring-orange-500 focus:border-transparent rounded-xl outline-none transition-all"
                    placeholder="Confirm new password"
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
                  >
                    {showConfirmPassword ? <FaEyeSlash size={18} /> : <FaEye size={18} />}
                  </button>
                  {errors.confirmPassword && (
                    <p className="mt-1.5 text-xs text-red-400">{errors.confirmPassword.message}</p>
                  )}
                </div>

                <button
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-gradient-to-r from-red-500 to-orange-500 hover:opacity-90 disabled:opacity-60 text-white font-semibold py-3 px-4 rounded-full flex items-center justify-center transition-opacity shadow-lg"
                >
                  {isLoading ? (
                    <svg
                      className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                  ) : (
                    'Reset Password'
                  )}
                </button>
              </form>

              {/* Helper Links */}
              <div className="mt-5 text-center text-xs">
                <span className="text-gray-400">
                  Remember your password?{' '}
                  <Link
                    href="/sign-in"
                    className="font-semibold text-orange-400 hover:text-orange-300"
                  >
                    Sign in here
                  </Link>
                </span>
              </div>
            </>
          ) : (
            <>
              {/* Success Message */}
              <div className="text-center">
                <div className="mb-6">
                  <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg
                      className="w-8 h-8 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M5 13l4 4L19 7"
                      ></path>
                    </svg>
                  </div>
                  <h1 className="text-3xl font-bold text-white mb-2">Password Reset!</h1>
                  <p className="text-gray-400">
                    Your password has been successfully reset. You can now sign in with your new
                    password.
                  </p>
                </div>

                <Link
                  href="/sign-in"
                  className="w-full bg-gradient-to-r from-red-500 to-orange-500 hover:opacity-90 text-white font-semibold py-3 px-4 rounded-full flex items-center justify-center transition-opacity shadow-lg"
                >
                  Sign In Now
                </Link>
              </div>
            </>
          )}
        </div>

        {/* Footer Links */}
        <div className="absolute bottom-6 sm:bottom-8 text-center w-full max-w-sm px-4">
          <div className="flex flex-col sm:flex-row justify-center items-center text-[11px] text-gray-500 space-y-1 sm:space-y-0 sm:space-x-3">
            <Link href="/terms" className="hover:text-gray-300">
              Terms & Conditions
            </Link>
            <span className="hidden sm:inline">|</span>
            <Link href="/privacy" className="hover:text-gray-300">
              Privacy Policy
            </Link>
          </div>
          <p className="mt-2 text-[11px] text-gray-600">
            © {new Date().getFullYear()} Typing.com.co. All rights reserved.
          </p>
        </div>
      </div>

      {/* CSS for dot pattern */}
      <style jsx global>{`
        .bg-dot-pattern {
          background-image: radial-gradient(circle, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
          background-size: 15px 15px;
        }
      `}</style>
    </div>
  );
}
