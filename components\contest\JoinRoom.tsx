'use client';

import { useState } from 'react';
import { FiArrowLeft, FiLink, FiSearch, FiUsers, FiClock, FiTarget } from 'react-icons/fi';

interface JoinRoomProps {
  onJoinRoom: (roomId: string) => void;
  onBack: () => void;
}

interface RoomInfo {
  id: string;
  name: string;
  participants: number;
  maxParticipants: number;
  difficulty: string;
  duration: number;
  status: 'waiting' | 'active';
  createdBy: string;
}

export default function JoinRoom({ onJoinRoom, onBack }: JoinRoomProps) {
  const [roomCode, setRoomCode] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [roomInfo, setRoomInfo] = useState<RoomInfo | null>(null);
  const [error, setError] = useState('');

  const handleSearch = async () => {
    if (!roomCode.trim()) return;
    
    setIsSearching(true);
    setError('');
    setRoomInfo(null);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock room data - in real app, this would come from API
      if (roomCode.toLowerCase() === 'demo123') {
        setRoomInfo({
          id: 'demo123',
          name: 'Speed Demons Challenge',
          participants: 4,
          maxParticipants: 8,
          difficulty: 'Hard',
          duration: 60,
          status: 'waiting',
          createdBy: 'TypeMaster2024'
        });
      } else {
        setError('Room not found. Please check the room code and try again.');
      }
    } catch (err) {
      setError('Failed to search for room. Please try again.');
    } finally {
      setIsSearching(false);
    }
  };

  const handleJoin = () => {
    if (roomInfo) {
      onJoinRoom(roomInfo.id);
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return 'text-green-400 bg-green-400/10 border-green-400/30';
      case 'medium': return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/30';
      case 'hard': return 'text-orange-400 bg-orange-400/10 border-orange-400/30';
      case 'expert': return 'text-red-400 bg-red-400/10 border-red-400/30';
      default: return 'text-gray-400 bg-gray-400/10 border-gray-400/30';
    }
  };

  const getStatusColor = (status: string) => {
    return status === 'active' 
      ? 'text-green-400 bg-green-400/10 border-green-400/30' 
      : 'text-blue-400 bg-blue-400/10 border-blue-400/30';
  };

  return (
    <div className="max-w-2xl mx-auto">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <button
          onClick={onBack}
          className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
        >
          <FiArrowLeft className="w-6 h-6" />
        </button>
        <div>
          <h1 className="text-3xl font-bold text-white">Join Contest Room</h1>
          <p className="text-gray-400">Enter a room code to join an existing contest</p>
        </div>
      </div>

      {/* Room Code Input */}
      <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6 mb-6">
        <label className="flex items-center gap-3 text-lg font-semibold text-white mb-4">
          <FiLink className="text-blue-400" />
          Room Code
        </label>
        
        <div className="flex gap-3">
          <input
            type="text"
            value={roomCode}
            onChange={(e) => setRoomCode(e.target.value.toUpperCase())}
            placeholder="Enter room code (e.g., DEMO123)"
            className="flex-1 bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none transition-colors"
            maxLength={10}
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
          />
          <button
            onClick={handleSearch}
            disabled={!roomCode.trim() || isSearching}
            className="px-6 py-3 bg-blue-500 hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg font-semibold transition-colors flex items-center gap-2"
          >
            {isSearching ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                Searching...
              </>
            ) : (
              <>
                <FiSearch className="w-4 h-4" />
                Search
              </>
            )}
          </button>
        </div>

        {/* Demo hint */}
        <div className="mt-4 p-3 bg-blue-500/10 border border-blue-500/30 rounded-lg">
          <p className="text-blue-400 text-sm">
            <strong>Demo:</strong> Try entering "DEMO123" to see how it works!
          </p>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-500/10 border border-red-500/30 rounded-xl p-4 mb-6">
          <p className="text-red-400">{error}</p>
        </div>
      )}

      {/* Room Information */}
      {roomInfo && (
        <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6 mb-6">
          <div className="flex justify-between items-start mb-6">
            <div>
              <h2 className="text-2xl font-bold text-white mb-2">{roomInfo.name}</h2>
              <p className="text-gray-400">Created by {roomInfo.createdBy}</p>
            </div>
            <span className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(roomInfo.status)}`}>
              {roomInfo.status.toUpperCase()}
            </span>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center p-4 bg-gray-700/50 rounded-lg">
              <FiUsers className="w-6 h-6 text-green-400 mx-auto mb-2" />
              <div className="text-lg font-bold text-white">{roomInfo.participants}/{roomInfo.maxParticipants}</div>
              <div className="text-sm text-gray-400">Participants</div>
            </div>
            
            <div className="text-center p-4 bg-gray-700/50 rounded-lg">
              <FiClock className="w-6 h-6 text-blue-400 mx-auto mb-2" />
              <div className="text-lg font-bold text-white">{roomInfo.duration}s</div>
              <div className="text-sm text-gray-400">Duration</div>
            </div>
            
            <div className="text-center p-4 bg-gray-700/50 rounded-lg">
              <FiTarget className="w-6 h-6 text-purple-400 mx-auto mb-2" />
              <div className={`text-lg font-bold px-2 py-1 rounded border ${getDifficultyColor(roomInfo.difficulty)}`}>
                {roomInfo.difficulty}
              </div>
              <div className="text-sm text-gray-400 mt-1">Difficulty</div>
            </div>
            
            <div className="text-center p-4 bg-gray-700/50 rounded-lg">
              <div className="w-6 h-6 bg-orange-400 rounded-full mx-auto mb-2"></div>
              <div className="text-lg font-bold text-white">{roomInfo.participants > 0 ? 'Active' : 'Empty'}</div>
              <div className="text-sm text-gray-400">Status</div>
            </div>
          </div>

          {/* Participants Preview */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-white mb-3">Current Participants</h3>
            <div className="flex gap-3">
              {[...Array(roomInfo.participants)].map((_, i) => (
                <div
                  key={i}
                  className="flex items-center gap-2 bg-gray-700 rounded-lg px-3 py-2"
                >
                  <div className="w-8 h-8 bg-gradient-to-br from-orange-400 to-red-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                    {String.fromCharCode(65 + i)}
                  </div>
                  <span className="text-white text-sm">Player {i + 1}</span>
                </div>
              ))}
              {roomInfo.participants < roomInfo.maxParticipants && (
                <div className="flex items-center gap-2 bg-gray-700/50 border-2 border-dashed border-gray-600 rounded-lg px-3 py-2">
                  <div className="w-8 h-8 border-2 border-gray-500 border-dashed rounded-full flex items-center justify-center text-gray-500 text-sm">
                    +
                  </div>
                  <span className="text-gray-500 text-sm">Available</span>
                </div>
              )}
            </div>
          </div>

          {/* Join Button */}
          <div className="flex gap-4">
            <button
              onClick={onBack}
              className="flex-1 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg font-semibold transition-colors"
            >
              Back
            </button>
            <button
              onClick={handleJoin}
              disabled={roomInfo.participants >= roomInfo.maxParticipants}
              className="flex-1 py-3 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg font-semibold transition-all transform hover:scale-105"
            >
              {roomInfo.participants >= roomInfo.maxParticipants ? 'Room Full' : 'Join Contest'}
            </button>
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="bg-gray-800/30 border border-gray-700 rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-3">How to Join</h3>
        <div className="space-y-2 text-gray-400">
          <p>• Ask the room creator for the room code</p>
          <p>• Enter the code in the field above</p>
          <p>• Click "Search" to find the room</p>
          <p>• Review the room details and click "Join Contest"</p>
        </div>
      </div>
    </div>
  );
}
