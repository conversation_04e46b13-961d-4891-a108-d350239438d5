'use client';

import { signIn, signOut, useSession } from 'next-auth/react';
import Link from 'next/link';
import { useState } from 'react';
import { FaSpinner } from 'react-icons/fa';

interface AuthButtonProps {
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showSignUp?: boolean;
}

export default function AuthButton({ 
  variant = 'primary', 
  size = 'md', 
  className = '',
  showSignUp = true 
}: AuthButtonProps) {
  const { data: session, status } = useSession();
  const [isSigningOut, setIsSigningOut] = useState(false);

  const handleSignOut = async () => {
    setIsSigningOut(true);
    try {
      await signOut({ callbackUrl: '/' });
    } catch (error) {
      console.error('Sign out error:', error);
    } finally {
      setIsSigningOut(false);
    }
  };

  const baseClasses = 'inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';
  
  const variantClasses = {
    primary: 'bg-gradient-to-r from-red-500 to-orange-500 hover:opacity-90 text-white focus:ring-orange-500',
    secondary: 'bg-gray-600 hover:bg-gray-700 text-white focus:ring-gray-500',
    outline: 'border-2 border-orange-500 text-orange-500 hover:bg-orange-500 hover:text-white focus:ring-orange-500'
  };

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  };

  const buttonClasses = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`;

  if (status === 'loading') {
    return (
      <div className={buttonClasses}>
        <FaSpinner className="animate-spin mr-2" />
        Loading...
      </div>
    );
  }

  if (status === 'authenticated') {
    return (
      <button
        onClick={handleSignOut}
        disabled={isSigningOut}
        className={`${buttonClasses} disabled:opacity-60`}
      >
        {isSigningOut ? (
          <>
            <FaSpinner className="animate-spin mr-2" />
            Signing out...
          </>
        ) : (
          'Sign Out'
        )}
      </button>
    );
  }

  return (
    <div className="flex gap-2">
      <Link href="/sign-in" className={buttonClasses}>
        Sign In
      </Link>
      {showSignUp && (
        <Link href="/sign-up" className={`${baseClasses} ${variantClasses.outline} ${sizeClasses[size]}`}>
          Sign Up
        </Link>
      )}
    </div>
  );
}
