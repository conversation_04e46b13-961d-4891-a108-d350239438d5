'use client';

import { useState, useEffect } from 'react';
import { FiUsers, FiPlus, FiLink, FiTrendingUp, FiClock } from 'react-icons/fi';

interface ContestLobbyProps {
  onCreateRoom: () => void;
  onJoinRoom: () => void;
}

interface ActiveRoom {
  id: string;
  name: string;
  participants: number;
  maxParticipants: number;
  difficulty: string;
  duration: number;
  status: 'waiting' | 'active';
  createdAt: Date;
}

export default function ContestLobby({ onCreateRoom, onJoinRoom }: ContestLobbyProps) {
  const [activeRooms, setActiveRooms] = useState<ActiveRoom[]>([]);
  const [stats, setStats] = useState({
    totalContests: 1247,
    activeRooms: 23,
    onlineUsers: 156
  });

  // Mock data for demonstration
  useEffect(() => {
    const mockRooms: ActiveRoom[] = [
      {
        id: '1',
        name: 'Speed Demons Challenge',
        participants: 4,
        maxParticipants: 8,
        difficulty: 'Hard',
        duration: 60,
        status: 'waiting',
        createdAt: new Date(Date.now() - 5 * 60000)
      },
      {
        id: '2',
        name: 'Beginner Friendly Race',
        participants: 2,
        maxParticipants: 6,
        difficulty: 'Easy',
        duration: 120,
        status: 'waiting',
        createdAt: new Date(Date.now() - 10 * 60000)
      },
      {
        id: '3',
        name: 'Pro Typing Championship',
        participants: 6,
        maxParticipants: 10,
        difficulty: 'Expert',
        duration: 90,
        status: 'active',
        createdAt: new Date(Date.now() - 15 * 60000)
      }
    ];
    setActiveRooms(mockRooms);
  }, []);

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return 'text-green-400 bg-green-400/10';
      case 'medium': return 'text-yellow-400 bg-yellow-400/10';
      case 'hard': return 'text-orange-400 bg-orange-400/10';
      case 'expert': return 'text-red-400 bg-red-400/10';
      default: return 'text-gray-400 bg-gray-400/10';
    }
  };

  const getStatusColor = (status: string) => {
    return status === 'active' ? 'text-green-400' : 'text-blue-400';
  };

  const formatTimeAgo = (date: Date) => {
    const minutes = Math.floor((Date.now() - date.getTime()) / 60000);
    return `${minutes}m ago`;
  };

  return (
    <div className="space-y-8">
      {/* Stats Section */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-gradient-to-br from-orange-500/10 to-red-500/10 border border-orange-500/20 rounded-xl p-6 text-center">
          <FiTrendingUp className="w-8 h-8 text-orange-400 mx-auto mb-3" />
          <div className="text-2xl font-bold text-white">{stats.totalContests.toLocaleString()}</div>
          <div className="text-gray-400">Total Contests</div>
        </div>
        <div className="bg-gradient-to-br from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-xl p-6 text-center">
          <FiUsers className="w-8 h-8 text-blue-400 mx-auto mb-3" />
          <div className="text-2xl font-bold text-white">{stats.activeRooms}</div>
          <div className="text-gray-400">Active Rooms</div>
        </div>
        <div className="bg-gradient-to-br from-green-500/10 to-teal-500/10 border border-green-500/20 rounded-xl p-6 text-center">
          <FiClock className="w-8 h-8 text-green-400 mx-auto mb-3" />
          <div className="text-2xl font-bold text-white">{stats.onlineUsers}</div>
          <div className="text-gray-400">Online Users</div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <button
          onClick={onCreateRoom}
          className="group bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white p-8 rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-2xl"
        >
          <FiPlus className="w-12 h-12 mx-auto mb-4 group-hover:rotate-90 transition-transform duration-300" />
          <h3 className="text-2xl font-bold mb-2">Create Room</h3>
          <p className="text-orange-100">Start a new typing contest and invite your friends</p>
        </button>

        <button
          onClick={onJoinRoom}
          className="group bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white p-8 rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-2xl"
        >
          <FiLink className="w-12 h-12 mx-auto mb-4 group-hover:rotate-12 transition-transform duration-300" />
          <h3 className="text-2xl font-bold mb-2">Join Room</h3>
          <p className="text-blue-100">Enter a room code to join an existing contest</p>
        </button>
      </div>

      {/* Active Rooms */}
      <div>
        <h2 className="text-2xl font-bold text-white mb-6 flex items-center gap-3">
          <FiUsers className="text-orange-400" />
          Active Rooms
        </h2>
        
        {activeRooms.length === 0 ? (
          <div className="text-center py-12 bg-gray-800/30 rounded-xl border border-gray-700">
            <FiUsers className="w-16 h-16 text-gray-500 mx-auto mb-4" />
            <p className="text-gray-400 text-lg">No active rooms at the moment</p>
            <p className="text-gray-500">Create a room to get started!</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {activeRooms.map((room) => (
              <div
                key={room.id}
                className="bg-gray-800/50 border border-gray-700 rounded-xl p-6 hover:border-orange-500/50 transition-all duration-300 cursor-pointer group"
              >
                <div className="flex justify-between items-start mb-4">
                  <h3 className="text-lg font-semibold text-white group-hover:text-orange-400 transition-colors">
                    {room.name}
                  </h3>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(room.status)}`}>
                    {room.status.toUpperCase()}
                  </span>
                </div>
                
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Participants:</span>
                    <span className="text-white">{room.participants}/{room.maxParticipants}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Duration:</span>
                    <span className="text-white">{room.duration}s</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Difficulty:</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(room.difficulty)}`}>
                      {room.difficulty}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Created:</span>
                    <span className="text-white">{formatTimeAgo(room.createdAt)}</span>
                  </div>
                </div>

                <div className="mt-4 pt-4 border-t border-gray-700">
                  <div className="flex justify-between items-center">
                    <div className="flex -space-x-2">
                      {[...Array(Math.min(room.participants, 4))].map((_, i) => (
                        <div
                          key={i}
                          className="w-8 h-8 bg-gradient-to-br from-orange-400 to-red-500 rounded-full border-2 border-gray-800 flex items-center justify-center text-white text-xs font-bold"
                        >
                          {String.fromCharCode(65 + i)}
                        </div>
                      ))}
                      {room.participants > 4 && (
                        <div className="w-8 h-8 bg-gray-600 rounded-full border-2 border-gray-800 flex items-center justify-center text-white text-xs">
                          +{room.participants - 4}
                        </div>
                      )}
                    </div>
                    <button className="px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-lg text-sm font-medium transition-colors">
                      {room.status === 'waiting' ? 'Join' : 'Spectate'}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
