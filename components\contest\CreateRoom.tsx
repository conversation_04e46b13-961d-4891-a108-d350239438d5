'use client';

import { useState } from 'react';
import { FiArrowLeft, FiUsers, FiClock, FiTarget, FiType } from 'react-icons/fi';

interface CreateRoomProps {
  onCreateRoom: (roomData: {
    name: string;
    createdBy: string;
    settings: {
      duration: number;
      difficulty: string;
      maxParticipants: number;
    };
  }) => void;
  onBack: () => void;
}

export default function CreateRoom({ onCreateRoom, onBack }: CreateRoomProps) {
  const [roomName, setRoomName] = useState('');
  const [duration, setDuration] = useState(60);
  const [difficulty, setDifficulty] = useState('Medium');
  const [maxParticipants, setMaxParticipants] = useState(8);
  const [isCreating, setIsCreating] = useState(false);

  const difficulties = [
    { name: 'Easy', description: 'Simple words, basic punctuation', color: 'green' },
    { name: 'Medium', description: 'Mixed complexity, moderate punctuation', color: 'yellow' },
    { name: 'Hard', description: 'Complex words, full punctuation', color: 'orange' },
    { name: 'Expert', description: 'Technical terms, symbols, numbers', color: 'red' }
  ];

  const durations = [30, 60, 90, 120, 180, 300];
  const participantOptions = [2, 4, 6, 8, 10, 12, 16, 20];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!roomName.trim()) return;

    setIsCreating(true);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    onCreateRoom({
      name: roomName.trim(),
      createdBy: 'current-user', // This would come from session
      settings: {
        duration,
        difficulty,
        maxParticipants
      }
    });
    
    setIsCreating(false);
  };

  const getDifficultyColor = (difficultyName: string) => {
    const diff = difficulties.find(d => d.name === difficultyName);
    switch (diff?.color) {
      case 'green': return 'border-green-500 bg-green-500/10 text-green-400';
      case 'yellow': return 'border-yellow-500 bg-yellow-500/10 text-yellow-400';
      case 'orange': return 'border-orange-500 bg-orange-500/10 text-orange-400';
      case 'red': return 'border-red-500 bg-red-500/10 text-red-400';
      default: return 'border-gray-500 bg-gray-500/10 text-gray-400';
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <button
          onClick={onBack}
          className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
        >
          <FiArrowLeft className="w-6 h-6" />
        </button>
        <div>
          <h1 className="text-3xl font-bold text-white">Create Contest Room</h1>
          <p className="text-gray-400">Set up your typing competition</p>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Room Name */}
        <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
          <label className="flex items-center gap-3 text-lg font-semibold text-white mb-4">
            <FiType className="text-orange-400" />
            Room Name
          </label>
          <input
            type="text"
            value={roomName}
            onChange={(e) => setRoomName(e.target.value)}
            placeholder="Enter a creative room name..."
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:border-orange-500 focus:outline-none transition-colors"
            maxLength={50}
            required
          />
          <div className="text-right text-sm text-gray-400 mt-2">
            {roomName.length}/50
          </div>
        </div>

        {/* Duration */}
        <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
          <label className="flex items-center gap-3 text-lg font-semibold text-white mb-4">
            <FiClock className="text-blue-400" />
            Contest Duration
          </label>
          <div className="grid grid-cols-3 md:grid-cols-6 gap-3">
            {durations.map((dur) => (
              <button
                key={dur}
                type="button"
                onClick={() => setDuration(dur)}
                className={`p-3 rounded-lg border-2 transition-all ${
                  duration === dur
                    ? 'border-blue-500 bg-blue-500/20 text-blue-400'
                    : 'border-gray-600 bg-gray-700 text-gray-300 hover:border-blue-400'
                }`}
              >
                <div className="text-lg font-bold">{dur}</div>
                <div className="text-xs">sec</div>
              </button>
            ))}
          </div>
        </div>

        {/* Difficulty */}
        <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
          <label className="flex items-center gap-3 text-lg font-semibold text-white mb-4">
            <FiTarget className="text-purple-400" />
            Difficulty Level
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {difficulties.map((diff) => (
              <button
                key={diff.name}
                type="button"
                onClick={() => setDifficulty(diff.name)}
                className={`p-4 rounded-lg border-2 text-left transition-all ${
                  difficulty === diff.name
                    ? getDifficultyColor(diff.name)
                    : 'border-gray-600 bg-gray-700 text-gray-300 hover:border-gray-500'
                }`}
              >
                <div className="font-semibold text-lg">{diff.name}</div>
                <div className="text-sm opacity-80">{diff.description}</div>
              </button>
            ))}
          </div>
        </div>

        {/* Max Participants */}
        <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
          <label className="flex items-center gap-3 text-lg font-semibold text-white mb-4">
            <FiUsers className="text-green-400" />
            Maximum Participants
          </label>
          <div className="grid grid-cols-4 md:grid-cols-8 gap-3">
            {participantOptions.map((count) => (
              <button
                key={count}
                type="button"
                onClick={() => setMaxParticipants(count)}
                className={`p-3 rounded-lg border-2 transition-all ${
                  maxParticipants === count
                    ? 'border-green-500 bg-green-500/20 text-green-400'
                    : 'border-gray-600 bg-gray-700 text-gray-300 hover:border-green-400'
                }`}
              >
                <div className="text-lg font-bold">{count}</div>
              </button>
            ))}
          </div>
        </div>

        {/* Create Button */}
        <div className="flex gap-4">
          <button
            type="button"
            onClick={onBack}
            className="flex-1 py-4 bg-gray-700 hover:bg-gray-600 text-white rounded-xl font-semibold transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={!roomName.trim() || isCreating}
            className="flex-1 py-4 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-xl font-semibold transition-all transform hover:scale-105"
          >
            {isCreating ? (
              <div className="flex items-center justify-center gap-2">
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                Creating Room...
              </div>
            ) : (
              'Create Room'
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
