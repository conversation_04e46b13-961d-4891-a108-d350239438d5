'use client';

import Header from '@/components/layout/Header/Header';
import { Inter } from 'next/font/google';
import Image from 'next/image';
import Link from 'next/link';
import { FiArrowUpRight } from 'react-icons/fi';

// Image paths
const GRADIENT_IMAGES = {
  grayBlack: '/gradient/gray-black.svg',
  orangeBlack: '/gradient/orange-black.svg',
} as const;

const HERO_IMAGES = {
  desktop: '/images/hero-image-desktop.svg',
  mobile: '/images/hero-image-mobile.svg',
} as const;

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  fallback: ['system-ui', 'arial'],
});

// Button styles constants
const BUTTON_STYLES = {
  base: 'group inline-flex items-center justify-center gap-1.5 rounded-full font-semibold transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-105 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2',
  size: 'px-5 py-2.5 text-xs sm:px-6 sm:py-3 sm:text-sm',
  variants: {
    light:
      'text-black bg-gradient-to-r from-[#FF4D00] to-[#FADCD3] hover:bg-gradient-to-r hover:from-black hover:to-gray-800 hover:text-white focus-visible:ring-[#FF4D00]',
    dark: 'text-[#FF4D00] bg-gradient-to-r from-[#1e1e1f] to-[#3a3a3c] hover:opacity-95 focus-visible:ring-[#FF4D00]',
  },
} as const;

interface CTAButtonProps {
  href: string;
  variant: 'light' | 'dark';
  label: string;
}

const CTAButton = ({ href, variant, label }: CTAButtonProps) => {
  const buttonClasses = `${inter.className} ${BUTTON_STYLES.base} ${BUTTON_STYLES.size} ${BUTTON_STYLES.variants[variant]}`;

  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    if (href === '/practice-typing') {
      e.preventDefault();
      // Scroll to the typing playground section
      const typingSection = document.getElementById('typing-practice-section');
      if (typingSection) {
        typingSection.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
        });
      }
    }
  };

  return (
    <Link href={href} className={buttonClasses} onClick={handleClick}>
      {label}
      <FiArrowUpRight className="w-3.5 h-3.5 sm:w-4 sm:h-4 transition-transform duration-200 group-hover:translate-x-0.5 group-hover:-translate-y-0.5" />
    </Link>
  );
};

const HeroSection = () => {
  return (
    <section className="relative min-h-screen z-[1] text-center overflow-hidden mt-0">
      {/* Gray-Black Gradient Overlay */}
      <div className="absolute inset-0">
        <Image
          src={GRADIENT_IMAGES.grayBlack}
          alt=""
          fill
          sizes="100vw"
          className="object-cover object-left-top opacity-70"
          priority
          unoptimized
        />
      </div>
      {/* Orange-Black Gradient Overlay */}
      <div className="absolute inset-0">
        <Image
          src={GRADIENT_IMAGES.orangeBlack}
          alt=""
          fill
          sizes="100vw"
          className="object-cover object-right-top opacity-75"
          priority
          unoptimized
        />
      </div>
      {/* Mobile Hero Image Container */}
      <div className="md:hidden absolute inset-3 rounded-xl sm:rounded-2xl z-[5] overflow-hidden">
        <Image
          src={HERO_IMAGES.mobile}
          alt="Hero background for mobile"
          fill
          sizes="calc(100vw - 1.5rem)"
          className="object-cover object-[center_35%]"
          priority
        />
      </div>

      {/* Desktop Hero Image Container */}
      <div className="hidden md:block absolute inset-3 rounded-2xl z-[5] overflow-hidden">
        <Image
          src={HERO_IMAGES.desktop}
          alt="Hero background for desktop"
          fill
          sizes="calc(100vw - 1.5rem)"
          className="object-cover object-[center_45%]"
          priority
        />
      </div>

      {/* Header Overlay - Fixed Position */}
      <div className="">
        <Header />
      </div>

      {/* Main Content - Perfectly Centered */}
      <div className="absolute inset-0 z-[10] flex items-center justify-center pointer-events-none">
        <div className="w-full max-w-xs sm:max-w-sm md:max-w-2xl lg:max-w-4xl xl:max-w-5xl px-6 pointer-events-auto text-center">
          <h1 className="hero-heading  text-4xl leading-tight mb-3 sm:text-5xl md:text-6xl md:mb-4 lg:text-[70px] lg:mb-5 xl:text-[80px] xl:mb-6 bg-gradient-to-r from-[#f92f17] via-[#e98870] to-[#f0efed] bg-clip-text text-transparent">
            Fast Fingers, Sharp Mind
          </h1>
          <p className="hero-text font-light text-white text-base mb-6 sm:text-lg md:text-xl md:mb-8 lg:text-[22px] lg:mb-10 xl:text-2xl">
            Let's Test Your Typing Speed Demon!
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-3 sm:gap-4">
            <CTAButton href="/practice-typing" variant="light" label="Practice Typing" />
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
