import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../../auth/[...nextauth]/route';

// In-memory storage for demo purposes
const rooms = new Map();

export async function POST(
  request: NextRequest,
  { params }: { params: { roomId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { roomId } = params;
    const room = rooms.get(roomId);

    if (!room) {
      return NextResponse.json(
        { success: false, error: 'Room not found' },
        { status: 404 }
      );
    }

    // Only room creator can start the contest
    if (room.createdBy !== session.user.email) {
      return NextResponse.json(
        { success: false, error: 'Only room creator can start the contest' },
        { status: 403 }
      );
    }

    // Check if room is already active or finished
    if (room.status !== 'waiting') {
      return NextResponse.json(
        { success: false, error: 'Contest already started or finished' },
        { status: 400 }
      );
    }

    // Start the contest
    room.status = 'active';
    room.startTime = new Date().toISOString();
    room.endTime = new Date(Date.now() + room.settings.duration * 1000).toISOString();

    // Initialize participant progress
    room.participants = room.participants.map((p: any) => ({
      ...p,
      progress: 0,
      wpm: 0,
      accuracy: 100,
      isFinished: false,
      startTime: new Date().toISOString()
    }));

    rooms.set(roomId, room);

    return NextResponse.json({
      success: true,
      room
    });
  } catch (error) {
    console.error('Error starting contest:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to start contest' },
      { status: 500 }
    );
  }
}
