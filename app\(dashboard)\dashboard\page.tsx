'use client';

import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart3,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>hai<PERSON>,
  Star,
  TrendingUp,
  Type,
} from 'lucide-react';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import React from 'react';
import {
  CartesianGrid,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

// --- DUMMY DATA (No changes here) ---
const performanceData = [
  { date: 'Oct 1', wpm: 75, accuracy: 98 },
  { date: 'Oct 2', wpm: 78, accuracy: 97 },
  { date: 'Oct 3', wpm: 77, accuracy: 99 },
  { date: 'Oct 4', wpm: 82, accuracy: 98 },
  { date: 'Oct 5', wpm: 85, accuracy: 97 },
  { date: 'Oct 6', wpm: 84, accuracy: 99 },
  { date: 'Oct 7', wpm: 88, accuracy: 98 },
];

const recentTests = [
  { id: 1, mode: 'Time 60s', wpm: 88, accuracy: '98%', date: '2h ago' },
  { id: 2, mode: 'Words 50', wpm: 84, accuracy: '99%', date: '1d ago' },
  { id: 3, mode: 'Time 30s', wpm: 91, accuracy: '97%', date: '1d ago' },
  { id: 4, mode: 'Quotes', wpm: 82, accuracy: '99%', date: '2d ago' },
];

// --- STYLED COMPONENTS (No changes here) ---

const StatCard = ({
  icon: Icon,
  title,
  value,
}: {
  icon: React.ElementType;
  title: string;
  value: string;
}) => (
  <div className="bg-white/5 p-5 rounded-2xl ring-1 ring-white/10 backdrop-blur-md">
    <div className="flex items-center gap-4">
      <div className="p-3 bg-gradient-to-br from-orange-500/20 to-orange-600/10 rounded-xl ring-1 ring-white/10">
        <Icon className="w-6 h-6 text-orange-400" />
      </div>
      <div>
        <p className="text-sm text-neutral-400">{title}</p>
        <p className="text-2xl font-bold text-white">{value}</p>
      </div>
    </div>
  </div>
);

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-black/50 p-4 rounded-xl ring-1 ring-white/10 backdrop-blur-lg">
        <p className="text-sm font-bold text-white mb-2">{`Date: ${label}`}</p>
        <p className="text-sm text-cyan-400">{`WPM: ${payload[0].value}`}</p>
        <p className="text-sm text-pink-400">{`Accuracy: ${payload[1].value}%`}</p>
      </div>
    );
  }
  return null;
};

// --- MAIN DASHBOARD PAGE ---
export default function DashboardPage() {
  const { data: session, status } = useSession();

  // State to ensure the component is only rendered on the client
  // This is a common pattern to avoid SSR issues with client-heavy libraries
  const [isClient, setIsClient] = React.useState(false);
  React.useEffect(() => {
    setIsClient(true);
  }, []);

  if (status === 'loading') {
    return (
      <div className="flex justify-center items-center min-h-screen bg-[#111319]">
        <div className="w-12 h-12 border-4 border-orange-500 border-t-transparent rounded-full animate-spin" />
      </div>
    );
  }

  if (status === 'unauthenticated' || !session?.user) {
    return (
      <div className="flex flex-col justify-center items-center min-h-screen bg-[#111319] text-white text-center px-4">
        <h1 className="text-3xl font-bold mb-4">Access Denied</h1>
        <p className="text-neutral-400 mb-8">Please log in to view your dashboard.</p>
        <Link
          href="/sign-in"
          className="px-6 py-3 font-semibold text-white bg-gradient-to-r from-orange-500 to-red-500 hover:brightness-110 rounded-full transition-all"
        >
          Go to Login
        </Link>
      </div>
    );
  }

  return (
    <div className="bg-[#111319] text-white min-h-screen font-sans relative overflow-hidden">
      {/* Background Aurora Glows */}
      <div className="absolute top-0 left-0 w-full h-full -z-10">
        <div className="absolute top-[-20%] left-[-20%] w-[50%] h-[50%] bg-orange-500/20 rounded-full blur-[150px] animate-pulse-slow" />
        <div className="absolute bottom-[-20%] right-[-20%] w-[50%] h-[50%] bg-cyan-500/10 rounded-full blur-[150px] animate-pulse-slow animation-delay-4000" />
      </div>

      <main className="max-w-[1600px] mx-auto p-4 sm:p-6 lg:p-8">
        {/* Header: Welcome message and Back to Home button */}
        <header className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-10">
          <div>
            <h1 className="text-3xl md:text-4xl font-bold text-white">Dashboard</h1>
            <p className="text-neutral-400 mt-1">
              Welcome back, {session.user.name || 'Racer'}. Let's get typing!
            </p>
          </div>
          <Link
            href="/"
            className="group mt-4 sm:mt-0 flex items-center gap-2 px-5 py-3 text-sm font-semibold text-white bg-gradient-to-br from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg shadow-orange-900/50"
          >
            <ArrowLeft className="w-5 h-5 transition-transform duration-300 group-hover:-translate-x-1" />
            <span>Back to Home</span>
          </Link>
        </header>

        {/* Main Grid Layout */}
        <div className="grid grid-cols-12 gap-6">
          {/* Left Column: Main Content */}
          <div className="col-span-12 lg:col-span-8 space-y-6">
            {/* Top Stat Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
              <StatCard icon={TrendingUp} title="Avg. WPM" value="86.4" />
              <StatCard icon={Crosshair} title="Avg. Accuracy" value="98.2%" />
              <StatCard icon={BarChart3} title="Tests Taken" value="241" />
              <StatCard icon={Clock} title="Time Typing" value="18h 4m" />
            </div>

            {/* Performance Chart */}
            <div className="bg-white/5 p-4 sm:p-6 rounded-2xl ring-1 ring-white/10 backdrop-blur-md">
              <h3 className="text-lg font-semibold text-white mb-4">Performance (Last 7 Days)</h3>
              <div className="h-[350px]">
                {/*  FIX: Only render the chart on the client-side */}
                {isClient && (
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={performanceData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="rgba(255, 255, 255, 0.1)" />
                      <XAxis dataKey="date" stroke="rgba(255, 255, 255, 0.5)" fontSize={12} />
                      <YAxis stroke="rgba(255, 255, 255, 0.5)" fontSize={12} />
                      <Tooltip content={<CustomTooltip />} />
                      <Line
                        type="monotone"
                        dataKey="wpm"
                        stroke="#22d3ee"
                        strokeWidth={2}
                        dot={{ r: 4, fill: '#22d3ee' }}
                        activeDot={{ r: 6 }}
                      />
                      <Line
                        type="monotone"
                        dataKey="accuracy"
                        stroke="#f472b6"
                        strokeWidth={2}
                        dot={{ r: 4, fill: '#f472b6' }}
                        activeDot={{ r: 6 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                )}
              </div>
            </div>
          </div>

          {/* Right Column: Sidebar (No changes here) */}
          <div className="col-span-12 lg:col-span-4 space-y-6">
            <div className="bg-white/5 p-6 rounded-2xl ring-1 ring-white/10 backdrop-blur-md">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                <Star className="text-yellow-400" /> Personal Bests
              </h3>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-neutral-400">WPM (60s)</span>
                  <span className="font-bold text-white">102</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-neutral-400">Raw WPM</span>
                  <span className="font-bold text-white">108</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-neutral-400">Accuracy</span>
                  <span className="font-bold text-white">100%</span>
                </div>
              </div>
            </div>
            <div className="bg-white/5 p-6 rounded-2xl ring-1 ring-white/10 backdrop-blur-md">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                <Type className="text-red-400" /> Keys to Practice
              </h3>
              <div className="flex gap-2">
                {['q', 'z', 'x', 'p'].map(key => (
                  <div
                    key={key}
                    className="w-10 h-10 flex items-center justify-center bg-black/20 rounded-lg font-mono text-lg font-bold text-red-300 ring-1 ring-red-500/50"
                  >
                    {key.toUpperCase()}
                  </div>
                ))}
              </div>
              <p className="text-xs text-neutral-500 mt-3">Based on your recent error rate.</p>
            </div>
            <div className="bg-white/5 p-6 rounded-2xl ring-1 ring-white/10 backdrop-blur-md">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                <BookCopy className="text-cyan-400" /> Recent Tests
              </h3>
              <ul className="space-y-3">
                {recentTests.map(test => (
                  <li
                    key={test.id}
                    className="flex items-center justify-between text-sm transition-all p-2 rounded-lg hover:bg-white/10"
                  >
                    <div>
                      <p className="font-semibold text-white">{test.mode}</p>
                      <p className="text-xs text-neutral-400">{test.date}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-cyan-400">{test.wpm} WPM</p>
                      <p className="text-xs text-neutral-400">{test.accuracy}</p>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </main>

      <style jsx>{`
        @keyframes pulse-slow {
          0%,
          100% {
            transform: scale(1);
            opacity: 0.15;
          }
          50% {
            transform: scale(1.1);
            opacity: 0.25;
          }
        }
        .animate-pulse-slow {
          animation: pulse-slow 8s infinite ease-in-out;
        }
        .animation-delay-4000 {
          animation-delay: -4s;
        }
      `}</style>
    </div>
  );
}
