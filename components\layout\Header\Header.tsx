'use client';

import { signOut, useSession } from 'next-auth/react';
import Image from 'next/image';
import Link from 'next/link';
import { useCallback, useEffect, useRef, useState } from 'react';
import { HiX } from 'react-icons/hi';

const NAV_ITEMS = [
  { name: 'Contest', href: '/contest' },
  { name: 'Leaderboard', href: '/leaderboard' },
  { name: 'Donate', href: '/donate', special: true },
  { name: 'About', href: '/about' },
  { name: 'Contact', href: '/contact' },
];

const defaultAvatar = '/images/user.svg';

const Header = () => {
  const { data: session, status } = useSession();
  const [isMobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [isUserMenuOpen, setUserMenuOpen] = useState(false);
  const [isSigningOut, setSigningOut] = useState(false);
  const userMenuRef = useRef<HTMLDivElement>(null);

  const toggleMobileMenu = useCallback(() => setMobileMenuOpen(prev => !prev), []);
  const closeMobileMenu = useCallback(() => setMobileMenuOpen(false), []);
  const toggleUserMenu = useCallback(() => setUserMenuOpen(prev => !prev), []);

  const handleSignOut = useCallback(async () => {
    setSigningOut(true);
    try {
      await signOut({ callbackUrl: '/' });
    } catch (error) {
      console.error('Sign out error:', error);
    } finally {
      setSigningOut(false);
      setUserMenuOpen(false);
    }
  }, []);

  useEffect(() => {
    const handleOutsideClick = (e: MouseEvent) => {
      if (userMenuRef.current && !userMenuRef.current.contains(e.target as Node)) {
        setUserMenuOpen(false);
      }
    };
    if (isUserMenuOpen) document.addEventListener('mousedown', handleOutsideClick);
    return () => document.removeEventListener('mousedown', handleOutsideClick);
  }, [isUserMenuOpen]);

  const renderAvatar = (size = 38) => {
    const imageUrl = session?.user?.image || defaultAvatar;
    return (
      <Image
        src={imageUrl || '/placeholder.svg'}
        alt={session?.user?.name || 'User Avatar'}
        width={size}
        height={size}
        className="rounded-full object-cover"
        unoptimized={imageUrl.startsWith('data:')}
      />
    );
  };

  return (
    <div className="relative z-[40] mt-2 md:mt-16 lg:mt-20">
      {/* Navigation Bar */}
      <div className="w-full flex justify-center py-[6px] px-2">
        <nav
          aria-label="Main Navigation"
          className="w-full max-w-6xl mx-auto rounded-[22px] px-4 py-[9px] flex justify-between items-center"
        >
          {/* Logo */}
          <Link
            href="/"
            onClick={closeMobileMenu}
            aria-label="Homepage"
            className="flex items-center group"
          >
            <img src="/logos/typing-logo-home-orange.svg" alt="" className="h-6" />
          </Link>

          {/* Desktop Navigation */}
          <ul className="hidden md:flex items-center bg-gradient-to-r from-[#ff4207] via-[#f5d0c5] to-[#ff4207] rounded-full p-[3px]">
            {NAV_ITEMS.map(({ name, href, special }) => (
              <li key={name} className="mx-[1px]">
                <Link
                  href={href}
                  className={`block text-xs font-bold rounded-full px-[18px] py-[6px] transition-all ${
                    special
                      ? 'bg-black text-white hover:bg-neutral-800'
                      : 'text-black hover:text-neutral-700'
                  }`}
                >
                  {name}
                </Link>
              </li>
            ))}
          </ul>

          {/* Mobile Menu Button */}
          <div className="md:hidden flex items-center">
            {/* Hamburger Menu Button */}
            <button
              onClick={toggleMobileMenu}
              aria-label={isMobileMenuOpen ? 'Close menu' : 'Open menu'}
              aria-expanded={isMobileMenuOpen}
              className="
                flex items-center justify-center
                w-[68px] h-[36px]
                rounded-full
                bg-gradient-to-r from-[#F05228] via-[#F58A6A] to-[#FCE3D8]
                hover:brightness-105
                text-white
                shadow-md
                transition-all duration-200
                focus:outline-none
              "
            >
              {isMobileMenuOpen ? (
                <HiX className="w-5 h-5" />
              ) : (
                <div
                  className="flex flex-col justify-center items-center gap-[4px] w-[28px]"
                  aria-hidden="true"
                >
                  <div className="h-[3px] w-[16px] bg-white rounded-full self-start"></div>
                  <div className="h-[3px] w-[28px] bg-white rounded-full"></div>
                  <div className="h-[3px] w-[16px] bg-white rounded-full self-end"></div>
                </div>
              )}
            </button>
          </div>

          {/* Desktop User Controls */}
          <div className="hidden md:flex items-center gap-3 relative" ref={userMenuRef}>
            {status === 'loading' && (
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 border-2 border-orange-500 border-t-transparent rounded-full animate-spin"></div>
              </div>
            )}

            {status === 'unauthenticated' && (
              <div className="flex items-center gap-3">
                <Link
                  href="/sign-in"
                  className="flex items-center gap-2 px-4 py-2 text-sm font-bold text-gray-800 hover:text-orange-600 border-2 border-gray-300 hover:border-orange-500 rounded-full transition-all duration-200 transform hover:scale-105 bg-white hover:bg-orange-50 shadow-md"
                >
                  Login
                </Link>
                <Link
                  href="/sign-up"
                  className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white text-sm font-semibold rounded-full transition-all duration-200 transform hover:scale-105 shadow-lg"
                >
                  Sign Up
                </Link>
              </div>
            )}

            {status === 'authenticated' && (
              <button
                onClick={toggleUserMenu}
                aria-label="User menu"
                className="focus:outline-none group"
              >
                <div className="flex items-center gap-3 p-2 rounded-full hover:bg-gray-100 transition-all duration-200">
                  <div className="w-[38px] h-[38px] rounded-full border-2 border-gray-300 group-hover:border-orange-500 overflow-hidden transition-all duration-200">
                    {renderAvatar()}
                  </div>
                  <div className="hidden lg:block text-left">
                    <p className="text-sm font-semibold text-gray-700 group-hover:text-orange-600 transition-colors">
                      {session?.user?.name || 'User'}
                    </p>
                    <p className="text-xs text-gray-500">View Profile</p>
                  </div>
                </div>
              </button>
            )}

            {/* Professional User Dropdown */}
            {isUserMenuOpen && status === 'authenticated' && (
              <div className="absolute right-0 top-full mt-3 w-64 bg-white rounded-2xl shadow-2xl border border-gray-100 py-2 z-[95] overflow-hidden">
                {/* User Info Header */}
                <div className="px-4 py-3 bg-gradient-to-r from-orange-50 to-red-50 border-b border-gray-100">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 rounded-full border-2 border-orange-500 overflow-hidden">
                      {renderAvatar(48)}
                    </div>
                    <div>
                      <p className="font-semibold text-gray-800">{session?.user?.name || 'User'}</p>
                      <p className="text-sm text-gray-600">{session?.user?.email}</p>
                    </div>
                  </div>
                </div>

                {/* Menu Items */}
                <div className="py-2">
                  <Link
                    href="/profile"
                    onClick={() => setUserMenuOpen(false)}
                    className="flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-orange-50 hover:text-orange-600 transition-all duration-200"
                  >
                    <div className="w-5 h-5 text-gray-500">👤</div>
                    <span className="font-medium">My Profile</span>
                  </Link>

                  <Link
                    href="/settings"
                    onClick={() => setUserMenuOpen(false)}
                    className="flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-orange-50 hover:text-orange-600 transition-all duration-200"
                  >
                    <div className="w-5 h-5 text-gray-500">⚙️</div>
                    <span className="font-medium">Settings</span>
                  </Link>

                  <Link
                    href="/dashboard"
                    onClick={() => setUserMenuOpen(false)}
                    className="flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-orange-50 hover:text-orange-600 transition-all duration-200"
                  >
                    <div className="w-5 h-5 text-gray-500">📊</div>
                    <span className="font-medium">Dashboard</span>
                  </Link>

                  <div className="border-t border-gray-100 mt-2 pt-2">
                    <button
                      onClick={handleSignOut}
                      disabled={isSigningOut}
                      className="flex items-center gap-3 w-full px-4 py-3 text-red-600 hover:bg-red-50 transition-all duration-200 disabled:opacity-60"
                    >
                      <div className="w-5 h-5">🚪</div>
                      <span className="font-medium">
                        {isSigningOut ? 'Signing out...' : 'Sign Out'}
                      </span>
                      {isSigningOut && (
                        <div className="w-4 h-4 border-2 border-red-600 border-t-transparent rounded-full animate-spin ml-auto"></div>
                      )}
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </nav>
      </div>

      {/* Mobile Navigation - Full Screen */}
      {isMobileMenuOpen && (
        <div className="md:hidden fixed inset-0 backdrop-blur-lg z-[100] flex flex-col py-6">
          {/* Header with Close Button */}
          <div className="flex justify-between items-center p-6 border-b border-neutral-800/50">
            <img src="/logos/typing-logo-home-orange.svg" alt="Typing Logo" className="h-8" />
            <button
              onClick={closeMobileMenu}
              aria-label="Close menu"
              className="flex items-center justify-center w-10 h-10 rounded-full hover:bg-neutral-700 text-white transition-all duration-200"
            >
              <HiX className="w-5 h-5" />
            </button>
          </div>

          {/* Main Content */}
          <div className="flex-1 flex flex-col justify-center px-6 py-8">
            {/* Navigation Links */}
            <div className="space-y-4 mb-12">
              {NAV_ITEMS.map(({ name, href, special }, index) => (
                <Link
                  key={name}
                  href={href}
                  onClick={closeMobileMenu}
                  className={`block text-2xl font-bold transition-all duration-300 transform hover:scale-105 ${
                    special
                      ? 'text-transparent bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text hover:from-orange-300 hover:to-red-400'
                      : 'text-white hover:text-orange-400'
                  }`}
                  style={{
                    animationDelay: `${index * 100}ms`,
                    animation: 'slideInFromRight 0.5s ease-out forwards',
                  }}
                >
                  {name}
                </Link>
              ))}
            </div>

            {/* User Section */}
            <div className="border-t border-neutral-800/50 pt-8">
              {status === 'loading' && (
                <div className="flex items-center justify-center py-8">
                  <div className="w-8 h-8 border-2 border-orange-500 border-t-transparent rounded-full animate-spin"></div>
                </div>
              )}

              {status === 'authenticated' && session?.user && (
                <div className="space-y-6">
                  <Link
                    href="/profile"
                    onClick={closeMobileMenu}
                    className="flex items-center gap-4 p-4 bg-gradient-to-r from-orange-50 to-red-50 rounded-2xl hover:from-orange-100 hover:to-red-100 transition-all duration-300 border border-orange-200"
                  >
                    <div className="w-12 h-12 rounded-full overflow-hidden border-2 border-orange-500">
                      {renderAvatar(48)}
                    </div>
                    <div>
                      <p className="text-gray-800 font-semibold text-lg">
                        {session.user.name || 'User'}
                      </p>
                      <p className="text-gray-600 text-sm">👤 View Profile</p>
                    </div>
                  </Link>

                  <div className="space-y-3">
                    <Link
                      href="/dashboard"
                      onClick={closeMobileMenu}
                      className="flex items-center gap-3 p-3 bg-white rounded-xl border border-gray-200 hover:border-orange-300 transition-all duration-300"
                    >
                      <span className="text-xl">📊</span>
                      <span className="text-gray-700 font-medium">Dashboard</span>
                    </Link>

                    <Link
                      href="/settings"
                      onClick={closeMobileMenu}
                      className="flex items-center gap-3 p-3 bg-white rounded-xl border border-gray-200 hover:border-orange-300 transition-all duration-300"
                    >
                      <span className="text-xl">⚙️</span>
                      <span className="text-gray-700 font-medium">Settings</span>
                    </Link>
                  </div>

                  <button
                    onClick={() => {
                      handleSignOut();
                      closeMobileMenu();
                    }}
                    disabled={isSigningOut}
                    className="w-full bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white py-4 rounded-2xl font-semibold text-lg disabled:opacity-60 transition-all duration-300 transform hover:scale-105 shadow-lg"
                  >
                    {isSigningOut ? (
                      <div className="flex items-center justify-center gap-2">
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        Signing out...
                      </div>
                    ) : (
                      '🚪 Sign Out'
                    )}
                  </button>
                </div>
              )}

              {status === 'unauthenticated' && (
                <div className="space-y-4">
                  <Link
                    href="/sign-in"
                    onClick={closeMobileMenu}
                    className="block w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white py-4 rounded-2xl text-center font-semibold text-lg transition-all duration-300 transform hover:scale-105 shadow-lg"
                  >
                    🔑 Login
                  </Link>
                  <Link
                    href="/sign-up"
                    onClick={closeMobileMenu}
                    className="block w-full border-2 border-orange-500 text-orange-500 hover:bg-orange-500 hover:text-white py-4 rounded-2xl text-center font-semibold text-lg transition-all duration-300"
                  >
                    ✨ Sign Up
                  </Link>
                </div>
              )}
            </div>
          </div>

          <style jsx>{`
            @keyframes slideInFromRight {
              from {
                opacity: 0;
                transform: translateX(30px);
              }
              to {
                opacity: 1;
                transform: translateX(0);
              }
            }
          `}</style>
        </div>
      )}
    </div>
  );
};

export default Header;
