import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../../auth/[...nextauth]/route';

// In-memory storage for demo purposes
const rooms = new Map();

export async function POST(
  request: NextRequest,
  { params }: { params: { roomId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { roomId } = params;
    const room = rooms.get(roomId);

    if (!room) {
      return NextResponse.json(
        { success: false, error: 'Room not found' },
        { status: 404 }
      );
    }

    const body = await request.json();
    const { progress, wpm, accuracy, isFinished } = body;

    // Find and update participant progress
    const participantIndex = room.participants.findIndex(
      (p: any) => p.id === session.user.email
    );

    if (participantIndex === -1) {
      return NextResponse.json(
        { success: false, error: 'Participant not found in room' },
        { status: 404 }
      );
    }

    // Update participant data
    room.participants[participantIndex] = {
      ...room.participants[participantIndex],
      progress: progress || 0,
      wpm: wpm || 0,
      accuracy: accuracy || 100,
      isFinished: isFinished || false,
      lastUpdate: new Date().toISOString()
    };

    // Check if all participants are finished or time is up
    const allFinished = room.participants.every((p: any) => p.isFinished);
    const timeUp = new Date() >= new Date(room.endTime);

    if ((allFinished || timeUp) && room.status === 'active') {
      room.status = 'finished';
      room.actualEndTime = new Date().toISOString();
      
      // Calculate final rankings
      room.participants.sort((a: any, b: any) => {
        if (b.progress !== a.progress) return b.progress - a.progress;
        if (b.wpm !== a.wpm) return b.wpm - a.wpm;
        return b.accuracy - a.accuracy;
      });

      // Assign positions
      room.participants = room.participants.map((p: any, index: number) => ({
        ...p,
        position: index + 1
      }));
    }

    rooms.set(roomId, room);

    return NextResponse.json({
      success: true,
      room,
      participant: room.participants[participantIndex]
    });
  } catch (error) {
    console.error('Error updating progress:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update progress' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { roomId: string } }
) {
  try {
    const { roomId } = params;
    const room = rooms.get(roomId);

    if (!room) {
      return NextResponse.json(
        { success: false, error: 'Room not found' },
        { status: 404 }
      );
    }

    // Return current progress of all participants
    return NextResponse.json({
      success: true,
      participants: room.participants,
      status: room.status,
      timeLeft: room.endTime ? Math.max(0, Math.floor((new Date(room.endTime).getTime() - Date.now()) / 1000)) : null
    });
  } catch (error) {
    console.error('Error fetching progress:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch progress' },
      { status: 500 }
    );
  }
}
