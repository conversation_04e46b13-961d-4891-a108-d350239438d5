'use client';

import { signIn } from 'next-auth/react';
import { useState } from 'react';
import { FaGithub, FaGoogle, FaSpinner } from 'react-icons/fa';
import { toast } from 'react-toastify';

interface SocialAuthButtonsProps {
  callbackUrl?: string;
  className?: string;
  buttonText?: 'signin' | 'signup';
}

export default function SocialAuthButtons({ 
  callbackUrl = '/dashboard',
  className = '',
  buttonText = 'signin'
}: SocialAuthButtonsProps) {
  const [loadingProvider, setLoadingProvider] = useState<string | null>(null);

  const handleSocialSignIn = async (provider: 'github' | 'google') => {
    setLoadingProvider(provider);
    try {
      await signIn(provider, { callbackUrl });
    } catch (error) {
      console.error(`${provider} sign in error:`, error);
      toast.error(`An error occurred with ${provider} ${buttonText}. Please try again.`);
      setLoadingProvider(null);
    }
  };

  const buttonBaseClasses = "w-full bg-[#2C2C2C] hover:bg-[#3A3A3A] disabled:opacity-60 text-gray-300 font-medium py-2.5 px-4 rounded-lg flex items-center justify-center transition-colors";

  const getButtonText = (provider: string) => {
    const action = buttonText === 'signin' ? 'Sign in' : 'Sign up';
    return `${action} with ${provider}`;
  };

  return (
    <div className={`space-y-3 ${className}`}>
      <button
        onClick={() => handleSocialSignIn('github')}
        disabled={loadingProvider !== null}
        className={buttonBaseClasses}
      >
        {loadingProvider === 'github' ? (
          <FaSpinner className="animate-spin mr-3" size={18} />
        ) : (
          <FaGithub className="mr-3 text-white" size={18} />
        )}
        {getButtonText('GitHub')}
      </button>
      
      <button
        onClick={() => handleSocialSignIn('google')}
        disabled={loadingProvider !== null}
        className={buttonBaseClasses}
      >
        {loadingProvider === 'google' ? (
          <FaSpinner className="animate-spin mr-3" size={18} />
        ) : (
          <FaGoogle className="mr-3 text-[#DB4437]" size={18} />
        )}
        {getButtonText('Google')}
      </button>
    </div>
  );
}
