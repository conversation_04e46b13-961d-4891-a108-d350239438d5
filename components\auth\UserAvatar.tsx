'use client';

import { useSession } from 'next-auth/react';
import Image from 'next/image';
import { useState } from 'react';
import { FaUser } from 'react-icons/fa';

interface UserAvatarProps {
  size?: number;
  className?: string;
  showName?: boolean;
  showEmail?: boolean;
  fallbackIcon?: boolean;
}

export default function UserAvatar({ 
  size = 40, 
  className = '',
  showName = false,
  showEmail = false,
  fallbackIcon = true
}: UserAvatarProps) {
  const { data: session } = useSession();
  const [imageError, setImageError] = useState(false);

  const user = session?.user;
  const imageUrl = user?.image;
  const userName = user?.name || 'User';
  const userEmail = user?.email;

  const avatarClasses = `rounded-full object-cover border-2 border-gray-300 ${className}`;

  const renderFallback = () => {
    if (fallbackIcon) {
      return (
        <div 
          className={`${avatarClasses} bg-gray-600 flex items-center justify-center`}
          style={{ width: size, height: size }}
        >
          <FaUser className="text-gray-300" size={size * 0.5} />
        </div>
      );
    }

    // Generate initials from name
    const initials = userName
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);

    return (
      <div 
        className={`${avatarClasses} bg-gradient-to-br from-orange-500 to-red-500 flex items-center justify-center text-white font-bold`}
        style={{ 
          width: size, 
          height: size,
          fontSize: size * 0.4
        }}
      >
        {initials}
      </div>
    );
  };

  const avatarElement = imageUrl && !imageError ? (
    <Image
      src={imageUrl}
      alt={`${userName}'s avatar`}
      width={size}
      height={size}
      className={avatarClasses}
      onError={() => setImageError(true)}
      unoptimized={imageUrl.startsWith('data:')}
    />
  ) : (
    renderFallback()
  );

  if (!showName && !showEmail) {
    return avatarElement;
  }

  return (
    <div className="flex items-center gap-3">
      {avatarElement}
      <div className="flex flex-col">
        {showName && (
          <span className="text-white font-semibold text-sm">
            {userName}
          </span>
        )}
        {showEmail && userEmail && (
          <span className="text-gray-400 text-xs">
            {userEmail}
          </span>
        )}
      </div>
    </div>
  );
}
