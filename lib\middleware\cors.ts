import { NextRequest, NextResponse } from 'next/server';

/**
 * Apply CORS headers to a response
 * @param req The incoming request
 * @param res The response to apply CORS headers to
 * @returns The response with CORS headers
 */
export function applyCors(req: NextRequest, res: NextResponse): NextResponse {
  // Get the origin from the request
  const origin = req.headers.get('origin') || '*';

  // Define allowed origins
  const allowedOrigins = [
    'http://localhost:3000',
    'https://typing.com.co',
    'https://www.typing.com.co',
    'https://typingcomco.vercel.app',
    'https://www.typingcomco.vercel.app',
  ];

  // Check if the origin is allowed
  const isAllowedOrigin = allowedOrigins.includes(origin) || origin === '*';

  // Set CORS headers
  res.headers.set('Access-Control-Allow-Credentials', 'true');
  res.headers.set('Access-Control-Allow-Origin', isAllowedOrigin ? origin : allowedOrigins[0]);
  res.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.headers.set(
    'Access-Control-Allow-Headers',
    'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization',
  );

  return res;
}
