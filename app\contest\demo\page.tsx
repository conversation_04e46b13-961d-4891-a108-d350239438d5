'use client';

import { useState } from 'react';
import ContestLobby from '@/components/contest/ContestLobby';
import CreateRoom from '@/components/contest/CreateRoom';
import JoinRoom from '@/components/contest/JoinRoom';
import ContestRoom from '@/components/contest/ContestRoom';
import ContestResults from '@/components/contest/ContestResults';

type DemoView = 'lobby' | 'create' | 'join' | 'room' | 'results';

const mockParticipants = [
  { id: '1', name: 'You', progress: 95, wpm: 78, accuracy: 96, position: 1, isFinished: true },
  { id: '2', name: 'SpeedTyper', progress: 88, wpm: 72, accuracy: 94, position: 2, isFinished: true },
  { id: '3', name: 'KeyboardNinja', progress: 82, wpm: 68, accuracy: 92, position: 3, isFinished: true },
  { id: '4', name: 'TypeMaster', progress: 75, wpm: 65, accuracy: 89, position: 4, isFinished: true },
];

const mockRoom = {
  id: 'demo123',
  name: 'Demo Contest Room',
  createdBy: 'demo-user',
  participants: ['user1', 'user2', 'user3'],
  settings: {
    duration: 60,
    difficulty: 'Medium',
    maxParticipants: 8
  },
  status: 'waiting' as const
};

export default function ContestDemoPage() {
  const [currentView, setCurrentView] = useState<DemoView>('lobby');

  const renderView = () => {
    switch (currentView) {
      case 'create':
        return (
          <CreateRoom
            onCreateRoom={(roomData) => {
              console.log('Room created:', roomData);
              setCurrentView('room');
            }}
            onBack={() => setCurrentView('lobby')}
          />
        );
      case 'join':
        return (
          <JoinRoom
            onJoinRoom={(roomId) => {
              console.log('Joining room:', roomId);
              setCurrentView('room');
            }}
            onBack={() => setCurrentView('lobby')}
          />
        );
      case 'room':
        return (
          <ContestRoom
            room={mockRoom}
            onLeaveRoom={() => setCurrentView('lobby')}
          />
        );
      case 'results':
        return (
          <ContestResults
            participants={mockParticipants}
            onBackToLobby={() => setCurrentView('lobby')}
            onPlayAgain={() => setCurrentView('room')}
          />
        );
      default:
        return (
          <ContestLobby
            onCreateRoom={() => setCurrentView('create')}
            onJoinRoom={() => setCurrentView('join')}
          />
        );
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Demo Navigation */}
          <div className="mb-8 text-center">
            <h1 className="text-3xl font-bold text-white mb-4">
              Contest Feature <span className="text-orange-500">Demo</span>
            </h1>
            <div className="flex flex-wrap justify-center gap-2 mb-4">
              <button
                onClick={() => setCurrentView('lobby')}
                className={`px-4 py-2 rounded-lg transition-colors ${
                  currentView === 'lobby'
                    ? 'bg-orange-500 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                Lobby
              </button>
              <button
                onClick={() => setCurrentView('create')}
                className={`px-4 py-2 rounded-lg transition-colors ${
                  currentView === 'create'
                    ? 'bg-orange-500 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                Create Room
              </button>
              <button
                onClick={() => setCurrentView('join')}
                className={`px-4 py-2 rounded-lg transition-colors ${
                  currentView === 'join'
                    ? 'bg-orange-500 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                Join Room
              </button>
              <button
                onClick={() => setCurrentView('room')}
                className={`px-4 py-2 rounded-lg transition-colors ${
                  currentView === 'room'
                    ? 'bg-orange-500 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                Contest Room
              </button>
              <button
                onClick={() => setCurrentView('results')}
                className={`px-4 py-2 rounded-lg transition-colors ${
                  currentView === 'results'
                    ? 'bg-orange-500 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                Results
              </button>
            </div>
            <p className="text-gray-400">
              Navigate through different views to see the contest feature in action
            </p>
          </div>

          {/* Current View */}
          {renderView()}
        </div>
      </div>
    </div>
  );
}
