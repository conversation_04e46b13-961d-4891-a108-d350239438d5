// TypingPlaygroundSection.tsx
'use client';

import {
  AlertTriangle,
  AtSign,
  ChevronDown,
  Clock,
  Hash,
  MessageSquare,
  RefreshCw,
  Settings,
  Type,
  Zap,
} from 'lucide-react';
import React from 'react';
import { FALLBACK_TEXT, LANGUAGES_FOR_DROPDOWN } from './TypingPlayground.config';
import { TypingTestResults, useTypingPlayground } from './TypingPlayground.hooks';
import {
  MODAL_BUTTON_CLASS,
  MODAL_CONTENT_CLASS,
  MODAL_OVERLAY_CLASS,
  MODAL_STATS_CONTAINER_CLASS,
  MODAL_STAT_ITEM_CLASS,
  MODAL_STAT_LABEL_CLASS,
  MODAL_STAT_VALUE_CLASS,
  MODAL_TITLE_CLASS,
  NEUE_MACHINA_FONT,
  TEXT_AREA_STYLE,
  getActiveLangButtonClass,
  getActivePillClass,
  getCharacterSpanClasses,
} from './TypingPlayground.styles';

const ResultsModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  results: TypingTestResults;
  onTryAgain: () => void;
  inputRef: React.RefObject<HTMLTextAreaElement>;
}> = ({ isOpen, onClose, results, onTryAgain, inputRef }) => {
  if (!isOpen) return null;

  const handleTryAgain = () => {
    onTryAgain();
    // Focus the input after a short delay to ensure the modal is closed
    setTimeout(() => {
      inputRef.current?.focus();
    }, 100);
  };

  return (
    <div className={MODAL_OVERLAY_CLASS}>
      <div className={MODAL_CONTENT_CLASS}>
        <h2 className={MODAL_TITLE_CLASS}>Test Results</h2>
        <div className={MODAL_STATS_CONTAINER_CLASS}>
          <div className={MODAL_STAT_ITEM_CLASS}>
            <span className={MODAL_STAT_LABEL_CLASS}>WPM:</span>
            <span className={MODAL_STAT_VALUE_CLASS}>{results.wpm}</span>
          </div>
          <div className={MODAL_STAT_ITEM_CLASS}>
            <span className={MODAL_STAT_LABEL_CLASS}>Accuracy:</span>
            <span className={MODAL_STAT_VALUE_CLASS}>{results.accuracy}%</span>
          </div>
          <div className={MODAL_STAT_ITEM_CLASS}>
            <span className={MODAL_STAT_LABEL_CLASS}>Time:</span>
            <span className={MODAL_STAT_VALUE_CLASS}>{results.time}s</span>
          </div>
          <div className={MODAL_STAT_ITEM_CLASS}>
            <span className={MODAL_STAT_LABEL_CLASS}>Mistakes:</span>
            <span className={MODAL_STAT_VALUE_CLASS}>{results.mistakes}</span>
          </div>
        </div>
        <div className="mt-6 flex justify-end space-x-3">
          <button onClick={handleTryAgain} className={MODAL_BUTTON_CLASS}>
            Try Again
          </button>
        </div>
      </div>
    </div>
  );
};

const TypingPlaygroundSection: React.FC = () => {
  const {
    isClient,
    currentText,
    userInput,
    timeElapsed,
    wpm,
    accuracy,
    mistakes,
    testActive,
    currentLanguage,
    selectedActualOtherLang,
    isLangDropdownOpen,
    testMode,
    testTimeOption,
    wordCountOption,
    showPunctuation,
    showNumbers,
    isLoading,
    rateLimitError,
    apiKeyError,
    showResultModal,
    setShowResultModal,
    finalResults,
    selectedCategory,
    inputRef,
    langDropdownRef,
    setTestMode,
    setTestTimeOption,
    setWordCountOption,
    setShowPunctuation,
    setShowNumbers,
    setSelectedCategory,
    handleInputChange,
    handleOptionChange,
    handleLanguageButtonClick,
    handleDropdownLangSelect,
    fetchAndResetText,
  } = useTypingPlayground();

  const renderTextToType = () => {
    // Centered error/loading messages
    const messageContainerStyle =
      'flex items-center justify-center text-center min-h-[inherit] w-full';

    if (isLoading && !currentText && !rateLimitError && !apiKeyError)
      return (
        <div className={messageContainerStyle}>
          <span className="text-gray-400 opacity-75">Initializing...</span>
        </div>
      );
    if (isClient && rateLimitError && !isLoading)
      return (
        <div className={messageContainerStyle}>
          <span className="text-yellow-400 flex items-center">
            <AlertTriangle size={18} className="mr-2 shrink-0" />
            {rateLimitError}
          </span>
        </div>
      );
    if (isLoading)
      // Loading new challenge specifically
      return (
        <div className={messageContainerStyle}>
          <span className="text-gray-400 opacity-75">Loading new challenge...</span>
        </div>
      );

    return currentText.split('').map((char, index) => {
      const isCurrentChar = index === userInput.length;
      const spanClasses = getCharacterSpanClasses(
        char,
        userInput[index],
        isCurrentChar,
        false,
        testActive,
      );
      const displayChar = char === ' ' ? '\u00A0' : char;

      const showCursor =
        isCurrentChar && !showResultModal && (!testActive || userInput.length < currentText.length);

      return (
        <span key={index} className={spanClasses}>
          {displayChar}
          {showCursor && (
            <span className="absolute left-0 -bottom-1 w-[1.5px] h-[1.1em] bg-orange-500 animate-pulse opacity-90"></span>
          )}
        </span>
      );
    });
  };

  const optionPillsConfig = [
    {
      label: 'punctuation',
      icon: <AtSign size={14} className="mr-1" />,
      isActive: showPunctuation,
      action: () => handleOptionChange(() => setShowPunctuation(!showPunctuation)),
    },
    {
      label: 'numbers',
      icon: <Hash size={14} className="mr-1" />,
      isActive: showNumbers,
      action: () => handleOptionChange(() => setShowNumbers(!showNumbers)),
    },
    { type: 'separator' as const },
    {
      label: 'time',
      icon: <Clock size={14} className="mr-1" />,
      isActive: testMode === 'time',
      action: () => handleOptionChange(() => setTestMode('time')),
    },
    {
      label: 'words',
      icon: <Type size={14} className="mr-1" />,
      isActive: testMode === 'words',
      action: () => handleOptionChange(() => setTestMode('words')),
    },
    {
      label: 'quote',
      icon: <MessageSquare size={14} className="mr-1" />,
      isActive: testMode === 'quote',
      action: () => handleOptionChange(() => setTestMode('quote')),
    },
    {
      label: 'custom',
      icon: <Settings size={14} className="mr-1" />,
      isActive: testMode === 'custom',
      action: () => handleOptionChange(() => setTestMode('custom')),
    },
  ];

  const timeOptions = [15, 30, 60, 120];
  const wordOptions = [10, 25, 50, 100]; // As per original image

  return (
    <section
      id="typing-practice-section"
      className="py-10 md:py-12 text-white min-h-screen flex flex-col items-center justify-center selection:bg-orange-600 selection:text-white"
      style={NEUE_MACHINA_FONT}
      onClick={e => {
        // Focus textarea only if not clicking on an interactive element like a button or dropdown
        if (
          e.target === e.currentTarget ||
          (e.target as HTMLElement).closest('.min-h-\\[90px\\]')
        ) {
          inputRef.current?.focus();
        }
      }}
    >
      <div className="container mx-auto px-4 w-full max-w-3xl xl:max-w-4xl flex flex-col items-center">
        {!showResultModal && (
          <>
            {/* Language Buttons & Dropdown */}
            <div className="flex justify-center mb-8 relative">
              <div
                className="flex rounded-full shadow-md bg-neutral-800 p-0.5"
                ref={langDropdownRef}
              >
                <button
                  onClick={() => handleLanguageButtonClick('english')}
                  className={`${getActiveLangButtonClass(
                    currentLanguage === 'english',
                  )} rounded-l-full`}
                >
                  english
                </button>
                <button
                  onClick={() => handleLanguageButtonClick('other')}
                  className={`${getActiveLangButtonClass(
                    currentLanguage === 'other',
                  )} rounded-r-full flex items-center`}
                >
                  other{' '}
                  <ChevronDown
                    size={16}
                    className={`ml-1.5 transform transition-transform duration-200 ${
                      isLangDropdownOpen ? 'rotate-180' : ''
                    }`}
                  />
                </button>
                {isLangDropdownOpen && currentLanguage === 'other' && (
                  <div className="absolute top-full mt-2 w-48 bg-neutral-700 rounded-md shadow-lg z-20 border border-neutral-600 overflow-hidden">
                    {LANGUAGES_FOR_DROPDOWN.map(lang => (
                      <button
                        key={lang}
                        onClick={() => handleDropdownLangSelect(lang)}
                        className={`block w-full text-left px-4 py-2.5 text-sm text-gray-200 hover:bg-neutral-600 transition-colors ${
                          selectedActualOtherLang === lang
                            ? 'bg-neutral-600 font-semibold text-orange-400'
                            : ''
                        }`}
                      >
                        {lang}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Text Display Area */}
            <div className="relative mb-3 w-full px-2 sm:px-0">
              <div
                className="min-h-[90px] sm:min-h-[105px] md:min-h-[120px] text-xl sm:text-2xl md:text-2xl leading-[1.8] sm:leading-[2] md:leading-[2.2] select-none text-left py-3 break-words tracking-[0.05em] font-mono whitespace-pre-wrap"
                aria-live="polite"
              >
                <div className="relative">{renderTextToType()}</div>
              </div>
              <textarea
                ref={inputRef}
                value={userInput}
                onChange={handleInputChange}
                disabled={
                  isLoading ||
                  showResultModal ||
                  rateLimitError !== null ||
                  (apiKeyError && currentText === FALLBACK_TEXT)
                }
                className="absolute inset-0 w-full h-full bg-transparent border-none outline-none resize-none text-transparent p-3 text-xl sm:text-2xl md:text-2xl text-left selection:bg-transparent tracking-[0.05em] leading-[1.8] sm:leading-[2] md:leading-[2.2] font-mono whitespace-pre-wrap"
                style={{
                  ...TEXT_AREA_STYLE,
                  caretColor: 'transparent',
                  fontFamily: "'Neue Machina', monospace",
                }}
                autoFocus
                spellCheck="false"
                autoCapitalize="none"
                autoCorrect="off"
                tabIndex={0}
                aria-label="Typing input area"
              />
            </div>

            {/* Refresh Button - Centered below text */}
            <div className="flex justify-center my-4 sm:my-5">
              <button
                onClick={() => fetchAndResetText(true)}
                disabled={isLoading}
                className={`p-2.5 rounded-full text-orange-500 hover:text-orange-400 disabled:text-gray-600 disabled:cursor-not-allowed transition-all duration-150 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 focus:ring-offset-neutral-900 hover:bg-neutral-800 active:bg-neutral-700`}
                title="New Text / Reset Test"
                aria-label="Refresh test text"
              >
                {isLoading ? (
                  <RefreshCw size={22} className="animate-spin sm:h-6 sm:w-6" />
                ) : (
                  <RefreshCw size={22} className="sm:h-6 sm:w-6" />
                )}
              </button>
            </div>

            {/* Combined Options Row in a single pill group */}
            <div className="flex flex-wrap justify-center items-center gap-1.5 mb-4 sm:mb-5 p-1  rounded-full shadow-sm mx-auto max-w-max">
              {optionPillsConfig.map((pill, index) =>
                pill.type === 'separator' ? (
                  <div key={`sep-${index}`} className="w-px h-5 bg-neutral-700 mx-1"></div>
                ) : (
                  <button
                    key={pill.label}
                    onClick={pill.action}
                    className={`${getActivePillClass(
                      pill.isActive,
                    )} flex items-center capitalize px-[10px] sm:px-3`} // Slightly more padding for aesthetics
                    aria-pressed={pill.isActive}
                  >
                    {pill.icon} {pill.label}
                  </button>
                ),
              )}
            </div>

            {/* Test Mode Specific Configs (Time/Word counts) */}
            <div className="flex justify-center items-center space-x-1.5 mb-6 sm:mb-8 text-sm min-h-[36px]">
              {' '}
              {/* min-h to prevent layout shift, increased slightly */}
              {testMode === 'time' &&
                timeOptions.map(opt => (
                  <button
                    key={`time-${opt}`}
                    onClick={() => handleOptionChange(() => setTestTimeOption(opt))}
                    className={`${getActivePillClass(testTimeOption === opt)} px-[10px] sm:px-3`}
                    aria-pressed={testTimeOption === opt}
                  >
                    {opt} sec
                  </button>
                ))}
              {testMode === 'words' &&
                wordOptions.map(opt => (
                  <button
                    key={`words-${opt}`}
                    onClick={() => handleOptionChange(() => setWordCountOption(opt))}
                    className={`${getActivePillClass(wordCountOption === opt)} px-[10px] sm:px-3`}
                    aria-pressed={wordCountOption === opt}
                  >
                    {opt} words
                  </button>
                ))}
              {/* No specific UI for quote or custom duration/word count */}
            </div>
          </>
        )}

        {/* Dynamic Timer / Stats Area - Shown only when test active or about to start */}
        <div
          className="h-10 text-center text-gray-300 flex items-center justify-center space-x-6 text-base sm:text-lg fixed bottom-5 left-1/2 -translate-x-1/2" // Fixed position at bottom
          style={{ ...NEUE_MACHINA_FONT, display: showResultModal ? 'none' : 'flex' }} // Hide if modal is open
        >
          {testActive && !showResultModal && (
            <>
              {testMode === 'time' && (
                <span className="text-orange-500">
                  {Math.max(0, Math.ceil(testTimeOption - timeElapsed))}s
                </span>
              )}
              <span>
                {wpm} <span className="text-xs text-gray-500">wpm</span>
              </span>
              <span>
                {accuracy}
                <span className="text-xs text-gray-500">%</span>
              </span>
            </>
          )}
          {!testActive &&
            !isLoading &&
            !showResultModal &&
            userInput.length === 0 &&
            !rateLimitError &&
            !apiKeyError && (
              <div className="flex items-center text-gray-400 text-sm">
                <Zap size={16} className="mr-1.5 text-orange-500" />
                {testMode === 'time'
                  ? `${testTimeOption} sec`
                  : testMode === 'words'
                    ? `${wordCountOption} words`
                    : testMode.charAt(0).toUpperCase() + testMode.slice(1)}{' '}
              </div>
            )}
        </div>

        {/* Global Error Messages Area - Shown below options if they occur */}
        {isClient && rateLimitError && !isLoading && !showResultModal && (
          <div className="my-2 p-3 bg-yellow-700 bg-opacity-30 border border-yellow-600 rounded-md text-yellow-300 flex items-center text-sm justify-center max-w-md w-full">
            <AlertTriangle size={18} className="mr-2 shrink-0" />
            <span>{rateLimitError} Please wait or refresh.</span>
          </div>
        )}
      </div>

      {showResultModal && finalResults && (
        <ResultsModal
          isOpen={showResultModal}
          onClose={() => setShowResultModal(false)}
          results={finalResults}
          onTryAgain={() => {
            setShowResultModal(false);
            fetchAndResetText(true);
          }}
          inputRef={inputRef}
        />
      )}
    </section>
  );
};

export default TypingPlaygroundSection;
