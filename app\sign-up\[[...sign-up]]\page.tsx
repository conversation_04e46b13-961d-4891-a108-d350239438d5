'use client';

import { zod<PERSON><PERSON>ol<PERSON> } from '@hookform/resolvers/zod';
import { Eye, EyeOff } from 'lucide-react';
import { signIn } from 'next-auth/react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { FaGithub, FaGoogle } from 'react-icons/fa';
import { FaLinkedinIn, FaXTwitter } from 'react-icons/fa6';
import { toast } from 'react-toastify';
import { z } from 'zod';

// Schema for registration
const signUpSchema = z
  .object({
    name: z.string().min(2, 'Full name must be at least 2 characters'),
    email: z.string().email('Please enter a valid email address'),
    password: z.string().min(6, 'Password must be at least 6 characters'),
    confirmPassword: z.string().min(6, 'Confirm password must be at least 6 characters'),
  })
  .refine(data => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

type SignUpFormData = z.infer<typeof signUpSchema>;

export default function SignUpPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<SignUpFormData>({ resolver: zodResolver(signUpSchema) });

  const onSubmit: SubmitHandler<SignUpFormData> = async data => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: data.name, email: data.email, password: data.password }),
      });
      if (response.ok) {
        toast.success('Account created successfully! Signing you in...');
        const result = await signIn('credentials', {
          email: data.email,
          password: data.password,
          redirect: false,
        });
        if (result?.error) {
          toast.error('Account created but auto sign-in failed. Please try signing in manually.');
          router.push('/sign-in');
        } else {
          router.push('/');
        }
      } else {
        const err = await response.json();
        toast.error(err.message || 'Failed to create account. Email might already be in use.');
      }
    } catch (e) {
      console.error('Sign up error:', e);
      toast.error('An error occurred during sign up. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSocialSignIn = async (provider: 'github' | 'google') => {
    setIsLoading(true);
    try {
      await signIn(provider, { callbackUrl: '/' });
    } catch {
      toast.error(`An error occurred with ${provider} sign in/up.`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-[#171717] flex font-sans">
      {/* Left Branding Panel */}
      <div className="hidden lg:flex w-1/2 bg-[#CC552E] bg-dot-pattern-orange p-12 items-center justify-center relative">
        <div className="flex items-center">
          <span className="text-7xl font-bold text-white tracking-tight">typing</span>
          <div className="ml-1 -mt-1 w-10 h-10 bg-black rounded-full flex items-center justify-center">
            <span className="text-white text-sm font-semibold">.co</span>
          </div>
        </div>
        <div className="absolute bottom-10 left-10 flex space-x-4">
          <Link href="#" aria-label="Twitter" className="text-gray-200 hover:text-white p-1">
            <FaXTwitter size={20} />
          </Link>
          <span className="text-gray-300 text-xl select-none">•</span>
          <Link href="#" aria-label="LinkedIn" className="text-gray-200 hover:text-white p-1">
            <FaLinkedinIn size={20} />
          </Link>
        </div>
      </div>

      {/* Right Form Panel */}
      <div className="w-full lg:w-1/2 bg-[#212121] flex flex-col items-center justify-center p-6 sm:p-10 relative">
        <div className="w-full max-w-sm">
          <h1 className="text-3xl font-bold text-white mb-2 text-center">Create Account</h1>
          <p className="text-gray-400 text-center mb-8">Join us and start typing!</p>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-5">
            <div>
              <input
                {...register('name')}
                type="text"
                placeholder="Full name"
                className="w-full px-4 py-3 bg-[#2C2C2C] text-gray-300 placeholder-gray-500 rounded-xl focus:ring-2 focus:ring-orange-500 outline-none transition-all"
              />
              {errors.name && <p className="mt-1 text-xs text-red-400">{errors.name.message}</p>}
            </div>

            <div>
              <input
                {...register('email')}
                type="email"
                placeholder="Email address"
                className="w-full px-4 py-3 bg-[#2C2C2C] text-gray-300 placeholder-gray-500 rounded-xl focus:ring-2 focus:ring-orange-500 outline-none transition-all"
              />
              {errors.email && <p className="mt-1 text-xs text-red-400">{errors.email.message}</p>}
            </div>

            <div className="relative">
              <input
                {...register('password')}
                type={showPassword ? 'text' : 'password'}
                placeholder="Password"
                className="w-full px-4 py-3 pr-12 bg-[#2C2C2C] text-gray-300 placeholder-gray-500 rounded-xl focus:ring-2 focus:ring-orange-500 outline-none transition-all"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
              >
                {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
              </button>
              {errors.password && (
                <p className="mt-1 text-xs text-red-400">{errors.password.message}</p>
              )}
            </div>

            <div className="relative">
              <input
                {...register('confirmPassword')}
                type={showConfirmPassword ? 'text' : 'password'}
                placeholder="Confirm password"
                className="w-full px-4 py-3 pr-12 bg-[#2C2C2C] text-gray-300 placeholder-gray-500 rounded-xl focus:ring-2 focus:ring-orange-500 outline-none transition-all"
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
              >
                {showConfirmPassword ? <EyeOff size={18} /> : <Eye size={18} />}
              </button>
              {errors.confirmPassword && (
                <p className="mt-1 text-xs text-red-400">{errors.confirmPassword.message}</p>
              )}
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full py-3 rounded-full font-semibold text-white bg-gradient-to-r from-red-500 to-orange-500 disabled:opacity-60 flex items-center justify-center shadow-lg transition-opacity"
            >
              {isLoading ? (
                <svg
                  className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
              ) : (
                'Create Account'
              )}
            </button>
          </form>

          <p className="mt-5 text-center text-xs text-gray-400">
            Already have an account?{' '}
            <Link href="/sign-in" className="font-semibold text-orange-400 hover:text-orange-300">
              Sign in here!
            </Link>
          </p>

          <div className="my-8 flex items-center">
            <hr className="flex-grow border-t border-gray-600" />
            <span className="mx-3 text-xs text-gray-500">OR</span>
            <hr className="flex-grow border-t border-gray-600" />
          </div>

          <div className="space-y-3">
            <button
              onClick={() => handleSocialSignIn('github')}
              disabled={isLoading}
              className="w-full py-2.5 rounded-lg bg-[#2C2C2C] hover:bg-[#3A3A3A] disabled:opacity-60 text-gray-300 font-medium flex items-center justify-center transition-colors"
            >
              <FaGithub className="mr-3 text-white" size={18} />
              Sign up with GitHub
            </button>
            <button
              onClick={() => handleSocialSignIn('google')}
              disabled={isLoading}
              className="w-full py-2.5 rounded-lg bg-[#2C2C2C] hover:bg-[#3A3A3A] disabled:opacity-60 text-gray-300 font-medium flex items-center justify-center transition-colors"
            >
              <FaGoogle className="mr-3 text-[#DB4437]" size={18} />
              Sign up with Google
            </button>
          </div>

          <div className="absolute bottom-6 sm:bottom-8 text-center w-full max-w-sm px-4">
            <div className="flex flex-col sm:flex-row justify-center items-center text-[11px] text-gray-500 space-y-1 sm:space-y-0 sm:space-x-3">
              <Link href="/terms" className="hover:text-gray-300">
                Terms & Conditions
              </Link>
              <span className="hidden sm:inline">|</span>
              <Link href="/privacy" className="hover:text-gray-300">
                Privacy Policy
              </Link>
            </div>
            <p className="mt-2 text-[11px] text-gray-600">
              © {new Date().getFullYear()} Typing.com.co. All rights reserved.
            </p>
          </div>
        </div>
      </div>

      {/* Global dot background pattern */}
      <style jsx global>{`
        .bg-dot-pattern-orange {
          background-image: radial-gradient(circle, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
          background-size: 15px 15px;
        }
      `}</style>
    </div>
  );
}
