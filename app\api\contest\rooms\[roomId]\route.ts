import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../auth/[...nextauth]/route';

// In-memory storage for demo purposes
// In production, use a proper database
const rooms = new Map();

export async function GET(
  request: NextRequest,
  { params }: { params: { roomId: string } }
) {
  try {
    const { roomId } = params;
    const room = rooms.get(roomId);

    if (!room) {
      return NextResponse.json(
        { success: false, error: 'Room not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      room
    });
  } catch (error) {
    console.error('Error fetching room:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch room' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { roomId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { roomId } = params;
    const room = rooms.get(roomId);

    if (!room) {
      return NextResponse.json(
        { success: false, error: 'Room not found' },
        { status: 404 }
      );
    }

    // Check if room is full
    if (room.participants.length >= room.settings.maxParticipants) {
      return NextResponse.json(
        { success: false, error: 'Room is full' },
        { status: 400 }
      );
    }

    // Check if user is already in the room
    const existingParticipant = room.participants.find(
      (p: any) => p.id === session.user.email
    );

    if (existingParticipant) {
      return NextResponse.json({
        success: true,
        room,
        message: 'Already in room'
      });
    }

    // Add participant to room
    room.participants.push({
      id: session.user.email,
      name: session.user.name || 'User',
      joinedAt: new Date().toISOString(),
      isReady: false
    });

    rooms.set(roomId, room);

    return NextResponse.json({
      success: true,
      room
    });
  } catch (error) {
    console.error('Error joining room:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to join room' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { roomId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { roomId } = params;
    const room = rooms.get(roomId);

    if (!room) {
      return NextResponse.json(
        { success: false, error: 'Room not found' },
        { status: 404 }
      );
    }

    // Remove participant from room
    room.participants = room.participants.filter(
      (p: any) => p.id !== session.user.email
    );

    // If room is empty or creator left, delete the room
    if (room.participants.length === 0 || room.createdBy === session.user.email) {
      rooms.delete(roomId);
      return NextResponse.json({
        success: true,
        message: 'Room deleted'
      });
    }

    rooms.set(roomId, room);

    return NextResponse.json({
      success: true,
      room
    });
  } catch (error) {
    console.error('Error leaving room:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to leave room' },
      { status: 500 }
    );
  }
}
