'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'react-toastify';
import { FaXTwitter, FaLinkedinIn } from 'react-icons/fa6';

const forgotPasswordSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
});

type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;

export default function ForgotPasswordPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema),
  });

  const onSubmit: SubmitHandler<ForgotPasswordFormData> = async (data) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: data.email }),
      });

      if (response.ok) {
        setIsSubmitted(true);
        toast.success('Password reset instructions sent to your email!');
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || 'An error occurred. Please try again.');
      }
    } catch (error) {
      console.error('Forgot password error:', error);
      toast.error('An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-[#171717] flex font-sans">
      {/* Left Branding Panel */}
      <div className="hidden lg:flex flex-col w-1/2 bg-dot-pattern items-center justify-center p-12 relative">
        {/* Logo */}
        <div className="flex items-center">
          <span className="text-7xl font-bold text-white tracking-tight">typing</span>
          <div className="ml-1 -mt-1 flex items-center justify-center w-10 h-10 bg-red-500 rounded-full">
            <span className="text-white text-sm font-semibold">.co</span>
          </div>
        </div>

        {/* Social Icons at the bottom */}
        <div className="absolute bottom-10 left-10 flex space-x-4">
          <Link href="#" aria-label="X (Twitter)" className="text-gray-400 hover:text-white p-1">
            <FaXTwitter size={20} />
          </Link>
          <span className="text-gray-500 text-xl leading-none select-none" aria-hidden="true">
            •
          </span>
          <Link href="#" aria-label="LinkedIn" className="text-gray-400 hover:text-white p-1">
            <FaLinkedinIn size={20} />
          </Link>
        </div>
      </div>

      {/* Right Form Panel */}
      <div className="w-full lg:w-1/2 bg-[#212121] flex flex-col items-center justify-center p-6 sm:p-10 relative">
        <div className="w-full max-w-sm">
          {!isSubmitted ? (
            <>
              {/* Header */}
              <div className="text-center mb-8">
                <h1 className="text-3xl font-bold text-white mb-2">Forgot Password?</h1>
                <p className="text-gray-400">Enter your email to reset your password</p>
              </div>

              {/* Form */}
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-5">
                <div>
                  <input
                    {...register('email')}
                    type="email"
                    className="w-full px-4 py-3 bg-[#2C2C2C] border border-transparent text-gray-300 placeholder-gray-500 focus:ring-2 focus:ring-orange-500 focus:border-transparent rounded-xl outline-none transition-all"
                    placeholder="Email address"
                  />
                  {errors.email && (
                    <p className="mt-1.5 text-xs text-red-400">{errors.email.message}</p>
                  )}
                </div>

                <button
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-gradient-to-r from-red-500 to-orange-500 hover:opacity-90 disabled:opacity-60 text-white font-semibold py-3 px-4 rounded-full flex items-center justify-center transition-opacity shadow-lg"
                >
                  {isLoading ? (
                    <svg
                      className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                  ) : (
                    'Send Reset Link'
                  )}
                </button>
              </form>

              {/* Helper Links */}
              <div className="mt-5 text-center text-xs">
                <span className="text-gray-400">
                  Remember your password?{' '}
                  <Link
                    href="/sign-in"
                    className="font-semibold text-orange-400 hover:text-orange-300"
                  >
                    Sign in here
                  </Link>
                </span>
              </div>
            </>
          ) : (
            <>
              {/* Success Message */}
              <div className="text-center">
                <div className="mb-6">
                  <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </div>
                  <h1 className="text-3xl font-bold text-white mb-2">Check Your Email</h1>
                  <p className="text-gray-400">
                    We've sent password reset instructions to your email address.
                  </p>
                </div>

                <div className="space-y-4">
                  <Link
                    href="/sign-in"
                    className="w-full bg-gradient-to-r from-red-500 to-orange-500 hover:opacity-90 text-white font-semibold py-3 px-4 rounded-full flex items-center justify-center transition-opacity shadow-lg"
                  >
                    Back to Sign In
                  </Link>
                  
                  <button
                    onClick={() => setIsSubmitted(false)}
                    className="w-full bg-[#2C2C2C] hover:bg-[#3A3A3A] text-gray-300 font-medium py-2.5 px-4 rounded-lg transition-colors"
                  >
                    Try Different Email
                  </button>
                </div>
              </div>
            </>
          )}
        </div>

        {/* Footer Links */}
        <div className="absolute bottom-6 sm:bottom-8 text-center w-full max-w-sm px-4">
          <div className="flex flex-col sm:flex-row justify-center items-center text-[11px] text-gray-500 space-y-1 sm:space-y-0 sm:space-x-3">
            <Link href="/terms" className="hover:text-gray-300">
              Terms & Conditions
            </Link>
            <span className="hidden sm:inline">|</span>
            <Link href="/privacy" className="hover:text-gray-300">
              Privacy Policy
            </Link>
          </div>
          <p className="mt-2 text-[11px] text-gray-600">
            © {new Date().getFullYear()} Typing.com.co. All rights reserved.
          </p>
        </div>
      </div>

      {/* CSS for dot pattern */}
      <style jsx global>{`
        .bg-dot-pattern {
          background-image: radial-gradient(circle, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
          background-size: 15px 15px;
        }
      `}</style>
    </div>
  );
}
