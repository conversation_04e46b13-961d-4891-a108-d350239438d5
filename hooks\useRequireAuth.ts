'use client';

import { useEffect } from 'react';
import { useAuth } from './useAuth';

interface UseRequireAuthOptions {
  redirectTo?: string;
  enabled?: boolean;
}

/**
 * Hook that redirects to sign-in page if user is not authenticated
 * @param options Configuration options
 * @returns Auth state and user data
 */
export function useRequireAuth(options: UseRequireAuthOptions = {}) {
  const { redirectTo = '/sign-in', enabled = true } = options;
  const auth = useAuth();

  useEffect(() => {
    if (!enabled) return;
    
    // Don't redirect while loading
    if (auth.isLoading) return;

    // Redirect if not authenticated
    if (!auth.isAuthenticated) {
      auth.redirectToSignIn();
    }
  }, [auth.isAuthenticated, auth.isLoading, auth.redirectToSignIn, enabled]);

  return auth;
}
