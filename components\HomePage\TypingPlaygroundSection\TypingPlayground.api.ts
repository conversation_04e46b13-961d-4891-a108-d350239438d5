// TypingPlayground.api.ts
import { GoogleGenerativeAI } from '@google/generative-ai';
import {
  DEFAULT_API_KEY_PLACEHOLDER,
  getRandomPracticeText,
  TextCategory,
} from './TypingPlayground.config';

let genAI: GoogleGenerativeAI | null = null;
let model: any = null;
export let initialApiKeyError = false;
let apiKeyExpired = false;

const initializeApi = () => {
  const apiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY || DEFAULT_API_KEY_PLACEHOLDER;

  if (!apiKey || apiKey === DEFAULT_API_KEY_PLACEHOLDER) {
    initialApiKeyError = true;
    return { genAI: null, model: null, apiKeyError: false, apiKey };
  }

  try {
    genAI = new GoogleGenerativeAI(apiKey);
    model = genAI.getGenerativeModel({
      model: 'gemini-2.0-flash',
      generationConfig: {
        temperature: 0.7,
        topP: 0.8,
        topK: 40,
        maxOutputTokens: 1024,
      },
    });
    return { genAI, model, apiKeyError: false, apiKey };
  } catch (error) {
    initialApiKeyError = true;
    return { genAI: null, model: null, apiKeyError: false, apiKey };
  }
};

// Initialize API on module load
const { apiKeyError } = initializeApi();

const calculateWordCount = (
  testMode: 'time' | 'words' | 'quote' | 'custom',
  testTimeOption: number,
  wordCountOption: number,
): number => {
  if (testMode === 'time') {
    // Average typing speed is 40-60 WPM
    const wordsPerMinute = 50;
    return Math.floor((testTimeOption / 60) * wordsPerMinute);
  } else if (testMode === 'words') {
    return wordCountOption;
  } else if (testMode === 'quote') {
    return 15 + Math.floor(Math.random() * 15); // 15-30 words for quotes
  }
  return 30; // Default for custom mode
};

export const generateApiPrompt = (category: TextCategory = 'general'): string => {
  const prompts = {
    general: `Generate a simple, easy-to-type paragraph with short sentences.
    The text should be suitable for beginners, using common words and simple punctuation.
    Keep it under 100 words and focus on readability.`,

    technical: `Generate a technical paragraph about programming or technology.
    Include some technical terms but keep it accessible.
    Use proper programming terminology and concepts.
    Keep it under 100 words and maintain a clear structure.`,

    creative: `Generate a creative, descriptive paragraph that paints a vivid picture.
    Use rich vocabulary and varied sentence structures.
    Focus on imagery and sensory details.
    Keep it under 100 words and maintain a flowing narrative.`,
  };

  return prompts[category] || prompts.general;
};

export const fetchTextFromApi = async (
  promptGenerator: () => string,
  category: TextCategory = 'general',
): Promise<{
  text: string;
  error?: string;
  isRateLimitError?: boolean;
  isApiKeyError?: boolean;
}> => {
  // If API key is expired or there was an initial error, silently use fallback
  if (apiKeyExpired || initialApiKeyError) {
    return {
      text: getRandomPracticeText(category),
      isApiKeyError: false,
    };
  }

  try {
    const prompt = promptGenerator();
    const result = await model.generateContent({
      contents: [
        {
          parts: [
            {
              text: `${prompt}\n\nImportant:
              1. Keep the text simple and easy to type
              2. Use only lowercase letters and basic punctuation
              3. Avoid special characters or numbers
              4. Keep sentences short and clear
              5. Make it suitable for a 30-second typing test`,
            },
          ],
        },
      ],
    });

    const response = await result.response;

    if (!response || !response.text()) {
      throw new Error('Empty response from API');
    }

    let newText = response
      .text()
      .trim()
      .toLowerCase() // Convert to lowercase
      .replace(/[*_`~#>|-]/g, '') // Remove special characters
      .replace(/\s\s+/g, ' ') // Remove extra spaces
      .replace(/\n+/g, ' ') // Replace newlines with spaces
      .replace(/[^a-z\s.,!?-]/g, '') // Keep only letters, basic punctuation, and spaces
      .replace(/\s+([.,!?-])/g, '$1') // Fix spacing around punctuation
      .replace(/([.,!?-])\s*/g, '$1 ') // Ensure space after punctuation
      .trim();

    // Remove common prefixes
    const prefixesToRemove = [
      "here's your text:",
      'here is your text:',
      "sure, here's the text:",
      "okay, here's the text:",
      'text:',
      'quote:',
      'here is the quote:',
    ];

    for (const prefix of prefixesToRemove) {
      if (newText.toLowerCase().startsWith(prefix)) {
        newText = newText.substring(prefix.length).trim();
      }
    }

    // Ensure the text is not too long (approximately 30 seconds of typing)
    const maxLength = 150; // Average typing speed of 40 WPM for 30 seconds
    if (newText.length > maxLength) {
      newText = newText.substring(0, maxLength).trim();
      // Find the last complete sentence
      const lastPeriod = newText.lastIndexOf('.');
      if (lastPeriod > 0) {
        newText = newText.substring(0, lastPeriod + 1);
      }
    }

    if (!newText || newText.length < 10) {
      throw new Error('API returned insufficient content');
    }

    return { text: newText };
  } catch (error: any) {
    const errorMessage = error.message?.toLowerCase() || '';
    const errorDetails = error.response?.data?.error?.details || [];

    // Check for API key expiration
    const isApiKeyExpired = errorDetails.some(
      (detail: any) =>
        detail.reason === 'API_KEY_INVALID' ||
        detail.message?.includes('API key expired') ||
        errorMessage.includes('api key expired'),
    );

    if (isApiKeyExpired) {
      apiKeyExpired = true;
      return {
        text: getRandomPracticeText(category),
        isApiKeyError: false,
      };
    }

    if (errorMessage.includes('rate limit') || errorMessage.includes('quota')) {
      return {
        text: getRandomPracticeText(category),
        isApiKeyError: false,
      };
    }

    if (errorMessage.includes('api key') || errorMessage.includes('permission')) {
      return {
        text: getRandomPracticeText(category),
        isApiKeyError: false,
      };
    }

    return {
      text: getRandomPracticeText(category),
      isApiKeyError: false,
    };
  }
};
