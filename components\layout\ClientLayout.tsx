'use client';

import { SessionProvider } from 'next-auth/react';
import dynamic from 'next/dynamic';
import React from 'react';

import Footer from './Footer/Footer';

const ScrollToTopButton = dynamic(() => import('@/components/ui/ScrollToTopButton'), {
  ssr: false,
});

interface ClientLayoutProps {
  children: React.ReactNode;
}

export default function ClientLayout({ children }: ClientLayoutProps) {
  return (
    <SessionProvider>
      <div className="flex flex-col min-h-screen">
        <main id="main-content" role="main" className="flex-grow">
          {children}
        </main>

        <footer role="contentinfo">
          <Footer />
        </footer>

        {/* Scroll to Top Button */}
        <ScrollToTopButton />
      </div>
    </SessionProvider>
  );
}
