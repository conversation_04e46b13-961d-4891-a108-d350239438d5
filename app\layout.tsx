import ClientLayout from '@/components/layout/ClientLayout';
import type { Metada<PERSON>, Viewport } from 'next';
import React from 'react';
import '../styles/globals.css';

export const metadata: Metadata = {
  metadataBase: new URL('https://typingcomco.vercel.app'),
  title: {
    default: 'Typing.com.co - Test Your Typing Speed | Free Online Typing Test',
    template: '%s | Typing.com.co - Free Online Typing Test',
  },
  description:
    'Boost your typing speed and accuracy with our interactive typing tests, lessons, and real-time stats. Free online typing test platform designed for learners, professionals, and competitive typists. Track your WPM and accuracy.',
  keywords: [
    'typing test',
    'typing speed',
    'typing practice',
    'touch typing',
    'wpm test',
    'keyboard skills',
    'typing lessons',
    'typing games',
    'free typing test',
    'online typing practice',
    'typing speed test',
    'typing accuracy',
    'typing competition',
    'typing skills',
    'typing improvement',
  ],
  authors: [{ name: 'Typing.com.co Team' }],
  creator: 'Typing.com.co',
  publisher: 'Typing.com.co',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    title: 'Typing.com.co - Test & Improve Typing Speed | Free Online Typing Test',
    description:
      'Test and improve your typing speed and accuracy online with interactive exercises. Free typing test platform with real-time feedback and detailed statistics.',
    url: 'https://typingcomco.vercel.app',
    siteName: 'Typing.com.co',
    type: 'website',
    locale: 'en_US',
    images: [
      {
        url: '/og-image.svg',
        width: 1200,
        height: 630,
        alt: 'Typing.com.co - Free Online Typing Speed Test Platform',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Typing.com.co - Interactive Typing Tests | Free Online Practice',
    description:
      'Interactive typing speed tests and practice lessons to boost your WPM. Free online typing test platform with detailed analytics.',
    creator: '@typingdotcomco',
    site: '@typingdotcomco',
    images: ['/og-image.svg'],
  },
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: 'any' },
      { url: '/icon.svg', type: 'image/svg+xml', sizes: 'any' },
      { url: '/icon-192.png', type: 'image/png', sizes: '192x192' },
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180' },
      { url: '/apple-touch-icon-152x152.png', sizes: '152x152' },
    ],
    other: [
      {
        rel: 'mask-icon',
        url: '/safari-pinned-tab.svg',
        color: '#000000',
      },
    ],
  },
  alternates: {
    canonical: 'https://typingcomco.vercel.app',
    languages: {
      'en-US': 'https://typingcomco.vercel.app',
    },
  },
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
  },
  category: 'education',
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#0A0A0A' },
  ],
  colorScheme: 'dark light',
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  const siteUrl = 'https://typingcomco.vercel.app'; // Define your site URL
  const orgName = 'Typing.com.co';

  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: orgName,
    url: siteUrl,
    logo: `${siteUrl}/icon-192.png`, // Or your main logo URL
    sameAs: [
      // Add links to your social media profiles if any
      // e.g., "https://twitter.com/typingdotcomco"
    ],
  };

  const websiteJsonLd = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: orgName,
    url: siteUrl,
    potentialAction: {
      '@type': 'SearchAction',
      target: `${siteUrl}/search?q={search_term_string}`,
      'query-input': 'required name=search_term_string',
    },
  };

  return (
    <html lang="en">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="dns-prefetch" href="https://typingcomco.vercel.app" />
        {/* Preload Neue Machina font to prevent font swap */}
        <link
          rel="preload"
          href="/fonts/Neue Machina.woff2"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="/fonts/Neue Machina.woff"
          as="font"
          type="font/woff"
          crossOrigin="anonymous"
        />
        {/* JSON-LD for Organization */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
        />
        {/* JSON-LD for WebSite */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(websiteJsonLd) }}
        />
      </head>
      <body>
        <ClientLayout>{children}</ClientLayout>
      </body>
    </html>
  );
}
