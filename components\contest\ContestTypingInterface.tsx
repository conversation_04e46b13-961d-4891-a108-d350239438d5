'use client';

import { useState, useEffect, useRef, useCallback } from 'react';

interface ContestTypingInterfaceProps {
  isActive: boolean;
  difficulty: string;
  onProgress: (progress: number, wpm: number, accuracy: number) => void;
}

const SAMPLE_TEXTS = {
  Easy: "The quick brown fox jumps over the lazy dog. This is a simple sentence for typing practice. Keep going and maintain your speed.",
  Medium: "Technology has revolutionized the way we communicate, work, and live. From smartphones to artificial intelligence, innovation continues to shape our future.",
  Hard: "Entrepreneurship requires dedication, perseverance, and strategic thinking. Successful entrepreneurs often face numerous challenges before achieving their goals.",
  Expert: "Quantum computing represents a paradigm shift in computational capabilities, leveraging quantum-mechanical phenomena like superposition and entanglement to process information."
};

export default function ContestTypingInterface({ isActive, difficulty, onProgress }: ContestTypingInterfaceProps) {
  const [text] = useState(SAMPLE_TEXTS[difficulty as keyof typeof SAMPLE_TEXTS] || SAMPLE_TEXTS.Medium);
  const [userInput, setUserInput] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [errors, setErrors] = useState(0);
  const [startTime, setStartTime] = useState<number | null>(null);
  const [wpm, setWpm] = useState(0);
  const [accuracy, setAccuracy] = useState(100);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isActive && inputRef.current) {
      inputRef.current.focus();
      if (!startTime) {
        setStartTime(Date.now());
      }
    }
  }, [isActive, startTime]);

  const calculateStats = useCallback(() => {
    if (!startTime) return;

    const timeElapsed = (Date.now() - startTime) / 1000 / 60; // in minutes
    const wordsTyped = userInput.length / 5; // standard word length
    const currentWpm = timeElapsed > 0 ? Math.round(wordsTyped / timeElapsed) : 0;
    const currentAccuracy = userInput.length > 0 ? Math.round(((userInput.length - errors) / userInput.length) * 100) : 100;
    const progress = Math.round((userInput.length / text.length) * 100);

    setWpm(currentWpm);
    setAccuracy(currentAccuracy);
    onProgress(progress, currentWpm, currentAccuracy);
  }, [startTime, userInput, errors, text.length, onProgress]);

  useEffect(() => {
    calculateStats();
  }, [calculateStats]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!isActive) return;

    const value = e.target.value;
    const newIndex = value.length;

    // Check for errors
    let newErrors = errors;
    if (newIndex > currentIndex) {
      // User typed a new character
      const typedChar = value[newIndex - 1];
      const expectedChar = text[newIndex - 1];
      if (typedChar !== expectedChar) {
        newErrors++;
        setErrors(newErrors);
      }
    }

    setUserInput(value);
    setCurrentIndex(newIndex);

    // Prevent typing beyond text length
    if (value.length >= text.length) {
      setUserInput(text);
      setCurrentIndex(text.length);
    }
  };

  const getCharacterClass = (index: number) => {
    if (index < userInput.length) {
      return userInput[index] === text[index] ? 'text-green-400 bg-green-400/20' : 'text-red-400 bg-red-400/20';
    } else if (index === userInput.length) {
      return 'bg-orange-400/50 animate-pulse'; // cursor
    }
    return 'text-gray-400';
  };

  const renderText = () => {
    return text.split('').map((char, index) => (
      <span
        key={index}
        className={`${getCharacterClass(index)} transition-all duration-150 ${
          char === ' ' ? 'mr-1' : ''
        }`}
      >
        {char === ' ' ? '\u00A0' : char}
      </span>
    ));
  };

  return (
    <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-white mb-4">Typing Challenge</h3>
        
        {/* Stats */}
        <div className="grid grid-cols-3 gap-4 mb-6">
          <div className="text-center p-3 bg-gray-700/50 rounded-lg">
            <div className="text-2xl font-bold text-orange-400">{wpm}</div>
            <div className="text-sm text-gray-400">WPM</div>
          </div>
          <div className="text-center p-3 bg-gray-700/50 rounded-lg">
            <div className="text-2xl font-bold text-green-400">{accuracy}%</div>
            <div className="text-sm text-gray-400">Accuracy</div>
          </div>
          <div className="text-center p-3 bg-gray-700/50 rounded-lg">
            <div className="text-2xl font-bold text-blue-400">{Math.round((userInput.length / text.length) * 100)}%</div>
            <div className="text-sm text-gray-400">Progress</div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-gray-600 rounded-full h-3 mb-6">
          <div
            className="bg-gradient-to-r from-orange-400 to-red-500 h-3 rounded-full transition-all duration-300"
            style={{ width: `${(userInput.length / text.length) * 100}%` }}
          ></div>
        </div>
      </div>

      {/* Text Display */}
      <div className="mb-6">
        <div className="bg-gray-900/50 border border-gray-600 rounded-lg p-6 font-mono text-lg leading-relaxed min-h-[120px] overflow-hidden">
          {renderText()}
        </div>
      </div>

      {/* Input Field */}
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={userInput}
          onChange={handleInputChange}
          disabled={!isActive}
          placeholder={isActive ? "Start typing..." : "Contest will start soon..."}
          className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:border-orange-500 focus:outline-none transition-colors font-mono text-lg disabled:opacity-50 disabled:cursor-not-allowed"
          maxLength={text.length}
        />
        
        {!isActive && (
          <div className="absolute inset-0 bg-gray-800/50 rounded-lg flex items-center justify-center">
            <span className="text-gray-400 font-medium">Waiting for contest to start...</span>
          </div>
        )}
      </div>

      {/* Instructions */}
      <div className="mt-4 text-sm text-gray-400 text-center">
        {isActive ? (
          "Type the text above as accurately and quickly as possible!"
        ) : (
          "Get ready to type when the contest begins"
        )}
      </div>
    </div>
  );
}
