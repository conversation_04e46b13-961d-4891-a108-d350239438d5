import { AboutSection, FAQSection, TestimonialsSection } from '@/components/HomePage';
import HeroSection from '@/components/HomePage/HeroSection/HeroSection';
import OpenAISection from '@/components/HomePage/OpenAISection/OpenAISection';
import TypingPlaygroundSection from '@/components/HomePage/TypingPlaygroundSection/TypingPlaygroundSection';

import Image from 'next/image';

const homePageBackground = '/gradient/home-page-background.svg';

export default function Home() {
  return (
    <div className="relative w-full">
      {/* Background Layer */}
      <div className="absolute inset-0 w-screen h-full">
        <Image
          src={homePageBackground}
          alt="Home background"
          fill
          sizes="100vw"
          className="object-cover w-full h-full"
          priority
          unoptimized
        />
      </div>

      {/* Content Layer */}
      <div className="relative z-[10]">
        <HeroSection />
        <OpenAISection />
        <TypingPlaygroundSection />
        <AboutSection />
        <TestimonialsSection />
        <FAQSection />
      </div>
    </div>
  );
}
