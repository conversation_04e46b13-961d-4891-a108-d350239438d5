'use client';

import { useState, useEffect } from 'react';
import { FiArrowUp } from 'react-icons/fi';

const ScrollToTopButton = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [scrollProgress, setScrollProgress] = useState(0);

  useEffect(() => {
    const toggleVisibility = () => {
      const scrolled = document.documentElement.scrollTop;
      const maxHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight;
      const progress = (scrolled / maxHeight) * 100;
      
      setScrollProgress(progress);
      setIsVisible(scrolled > 300);
    };

    window.addEventListener('scroll', toggleVisibility);
    return () => window.removeEventListener('scroll', toggleVisibility);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };

  if (!isVisible) return null;

  return (
    <button
      onClick={scrollToTop}
      className="fixed bottom-6 right-6 z-50 group"
      aria-label="Scroll to top"
    >
      {/* Progress Ring */}
      <div className="relative w-14 h-14">
        {/* Background Circle */}
        <svg
          className="absolute inset-0 w-full h-full transform -rotate-90"
          viewBox="0 0 56 56"
        >
          <circle
            cx="28"
            cy="28"
            r="24"
            fill="none"
            stroke="rgba(255, 77, 0, 0.1)"
            strokeWidth="2"
          />
          {/* Progress Circle */}
          <circle
            cx="28"
            cy="28"
            r="24"
            fill="none"
            stroke="url(#orangeGradient)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeDasharray={`${2 * Math.PI * 24}`}
            strokeDashoffset={`${2 * Math.PI * 24 * (1 - scrollProgress / 100)}`}
            className="transition-all duration-300 ease-out"
          />
          {/* Gradient Definition */}
          <defs>
            <linearGradient id="orangeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#FF4D00" />
              <stop offset="50%" stopColor="#FF6B1A" />
              <stop offset="100%" stopColor="#FF8533" />
            </linearGradient>
          </defs>
        </svg>

        {/* Button Content */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-12 h-12 bg-gradient-to-br from-[#FF4D00] to-[#FF8533] rounded-full shadow-lg hover:shadow-xl transition-all duration-300 ease-out transform group-hover:scale-110 group-active:scale-95 flex items-center justify-center backdrop-blur-sm">
            <FiArrowUp 
              className="w-5 h-5 text-white transition-transform duration-300 group-hover:-translate-y-0.5" 
            />
          </div>
        </div>

        {/* Glow Effect */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#FF4D00] to-[#FF8533] rounded-full opacity-0 group-hover:opacity-20 transition-opacity duration-300 blur-md scale-110"></div>
      </div>

      {/* Tooltip */}
      <div className="absolute bottom-full right-0 mb-2 px-3 py-1 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap pointer-events-none">
        Scroll to top
        <div className="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
      </div>
    </button>
  );
};

export default ScrollToTopButton;
