import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/route';

// In-memory storage for demo purposes
// In production, use a proper database
const rooms = new Map();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    
    // Filter rooms by status if provided
    const filteredRooms = Array.from(rooms.values()).filter(room => {
      if (status) {
        return room.status === status;
      }
      return true;
    });

    return NextResponse.json({
      success: true,
      rooms: filteredRooms
    });
  } catch (error) {
    console.error('Error fetching rooms:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch rooms' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { name, settings } = body;

    if (!name || !settings) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Generate room ID
    const roomId = Math.random().toString(36).substr(2, 9).toUpperCase();

    const room = {
      id: roomId,
      name,
      createdBy: session.user.email,
      createdAt: new Date().toISOString(),
      settings: {
        duration: settings.duration || 60,
        difficulty: settings.difficulty || 'Medium',
        maxParticipants: settings.maxParticipants || 8
      },
      participants: [
        {
          id: session.user.email,
          name: session.user.name || 'User',
          joinedAt: new Date().toISOString(),
          isReady: false
        }
      ],
      status: 'waiting',
      startTime: null,
      endTime: null
    };

    rooms.set(roomId, room);

    return NextResponse.json({
      success: true,
      room
    });
  } catch (error) {
    console.error('Error creating room:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create room' },
      { status: 500 }
    );
  }
}
