'use client';

import { useSession, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useCallback, useMemo } from 'react';

export interface AuthUser {
  id: string;
  email: string;
  name: string;
  image?: string;
}

export interface UseAuthReturn {
  user: AuthUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  signOut: () => Promise<void>;
  redirectToSignIn: (returnUrl?: string) => void;
  redirectToDashboard: () => void;
}

export function useAuth(): UseAuthReturn {
  const { data: session, status } = useSession();
  const router = useRouter();

  const user = useMemo(() => {
    if (!session?.user) return null;
    
    return {
      id: session.user.id,
      email: session.user.email,
      name: session.user.name,
      image: session.user.image,
    } as AuthUser;
  }, [session]);

  const isAuthenticated = status === 'authenticated' && !!user;
  const isLoading = status === 'loading';

  const handleSignOut = useCallback(async () => {
    try {
      await signOut({ callbackUrl: '/' });
    } catch (error) {
      console.error('Sign out error:', error);
      throw error;
    }
  }, []);

  const redirectToSignIn = useCallback((returnUrl?: string) => {
    const currentPath = returnUrl || window.location.pathname;
    const redirectUrl = `/sign-in?redirect=${encodeURIComponent(currentPath)}`;
    router.push(redirectUrl);
  }, [router]);

  const redirectToDashboard = useCallback(() => {
    router.push('/dashboard');
  }, [router]);

  return {
    user,
    isAuthenticated,
    isLoading,
    signOut: handleSignOut,
    redirectToSignIn,
    redirectToDashboard,
  };
}
