# Authentication Components

This directory contains reusable authentication components and hooks for the NextAuth.js integration.

## Components

### AuthButton
A versatile button component that automatically shows sign-in/sign-up or sign-out based on authentication status.

```tsx
import { AuthButton } from '@/components/auth';

// Basic usage
<AuthButton />

// With variants and sizes
<AuthButton variant="primary" size="lg" />
<AuthButton variant="outline" size="sm" showSignUp={false} />
```

**Props:**
- `variant`: 'primary' | 'secondary' | 'outline'
- `size`: 'sm' | 'md' | 'lg'
- `className`: Additional CSS classes
- `showSignUp`: Whether to show sign-up button when not authenticated

### AuthGuard
Protects routes and content based on authentication status.

```tsx
import { AuthGuard } from '@/components/auth';

// Protect content (requires authentication)
<AuthGuard requireAuth={true}>
  <ProtectedContent />
</AuthGuard>

// Public content (no authentication required)
<AuthGuard requireAuth={false}>
  <PublicContent />
</AuthGuard>
```

**Props:**
- `requireAuth`: Whether authentication is required
- `redirectTo`: Where to redirect if not authenticated
- `fallback`: Custom loading component

### ProtectedRoute
Higher-order component for protecting entire pages/routes.

```tsx
import { ProtectedRoute } from '@/components/auth';

export default function DashboardPage() {
  return (
    <ProtectedRoute>
      <div>Protected dashboard content</div>
    </ProtectedRoute>
  );
}
```

### SocialAuthButtons
Pre-styled social authentication buttons for GitHub and Google.

```tsx
import { SocialAuthButtons } from '@/components/auth';

// For sign-in page
<SocialAuthButtons buttonText="signin" callbackUrl="/dashboard" />

// For sign-up page
<SocialAuthButtons buttonText="signup" callbackUrl="/onboarding" />
```

### UserAvatar
Displays user avatar with optional name and email.

```tsx
import { UserAvatar } from '@/components/auth';

// Basic avatar
<UserAvatar size={40} />

// With user info
<UserAvatar size={48} showName showEmail />

// Custom styling
<UserAvatar size={32} className="border-2 border-orange-500" />
```

## Hooks

### useAuth
Main authentication hook providing user state and auth methods.

```tsx
import { useAuth } from '@/hooks';

function MyComponent() {
  const { 
    user, 
    isAuthenticated, 
    isLoading, 
    signOut, 
    redirectToSignIn,
    redirectToDashboard 
  } = useAuth();

  if (isLoading) return <div>Loading...</div>;
  
  if (!isAuthenticated) {
    return <button onClick={() => redirectToSignIn()}>Sign In</button>;
  }

  return (
    <div>
      <p>Welcome, {user.name}!</p>
      <button onClick={signOut}>Sign Out</button>
    </div>
  );
}
```

### useRequireAuth
Hook that automatically redirects to sign-in if not authenticated.

```tsx
import { useRequireAuth } from '@/hooks';

function ProtectedComponent() {
  const { user, isLoading } = useRequireAuth();

  if (isLoading) return <div>Loading...</div>;

  // This will only render if user is authenticated
  return <div>Hello, {user.name}!</div>;
}
```

## Usage Examples

### Protecting a Page
```tsx
// app/dashboard/page.tsx
import { ProtectedRoute } from '@/components/auth';

export default function DashboardPage() {
  return (
    <ProtectedRoute>
      <div className="dashboard">
        <h1>Dashboard</h1>
        {/* Dashboard content */}
      </div>
    </ProtectedRoute>
  );
}
```

### Conditional Rendering
```tsx
import { useAuth, AuthGuard } from '@/components/auth';

function HomePage() {
  return (
    <div>
      <h1>Welcome to Typing.com.co</h1>
      
      {/* Show different content based on auth status */}
      <AuthGuard requireAuth={false}>
        <div>Public content for everyone</div>
      </AuthGuard>
      
      <AuthGuard requireAuth={true}>
        <div>Private content for authenticated users</div>
      </AuthGuard>
    </div>
  );
}
```

### Custom Authentication Flow
```tsx
import { useAuth, SocialAuthButtons } from '@/components/auth';

function CustomSignIn() {
  const { isAuthenticated, redirectToDashboard } = useAuth();

  if (isAuthenticated) {
    redirectToDashboard();
    return null;
  }

  return (
    <div className="sign-in-form">
      <h2>Sign In</h2>
      
      {/* Email/Password form here */}
      
      <div className="divider">OR</div>
      
      <SocialAuthButtons 
        buttonText="signin" 
        callbackUrl="/dashboard" 
      />
    </div>
  );
}
```

## Integration with Existing Components

The authentication components are designed to work seamlessly with your existing UI. They use the same styling patterns and are fully responsive.

All components are already integrated with NextAuth.js and will automatically handle:
- Session management
- Loading states
- Error handling
- Redirects
- Social authentication

## Demo

Visit `/auth-demo` to see all components in action and test the authentication flow.
